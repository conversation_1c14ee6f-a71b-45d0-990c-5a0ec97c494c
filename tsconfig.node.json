{"extends": "@tsconfig/node22/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "composite": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "allowSyntheticDefaultImports": true}}