import { defineConfig } from 'vite';
import vue from "@vitejs/plugin-vue";
import laravel from 'laravel-vite-plugin';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

export default defineConfig({
  define: {
    'import.meta.env.VITE_SOCKET_SERVER_PORT': JSON.stringify(process.env.SOCKET_SERVER_PORT || '6001'),
    'import.meta.env.VITE_APP_URL': JSON.stringify(process.env.APP_URL || 'http://localhost'),
  },
  plugins: [
    vue({
      template: {
        transformAssetUrls: {
          base: null,
          includeAbsolute: false
        }
      }
    }),
    laravel({
      input: 'resources/js/app.ts',
      refresh: true
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'resources/js'),
      'ziggy-js': path.resolve('vendor/tightenco/ziggy')
    }
  }
});
