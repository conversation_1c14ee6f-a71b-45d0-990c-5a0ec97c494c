<?php

namespace App\Providers;

use App\Http\Middleware\ApiLocaleMiddleware;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->mapApiRoutes();
        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes(): void
    {
        // Admin routing
        Route::prefix('admin')
            ->middleware(['web'])
            ->namespace('Src\Domain\Admin\Controllers')
            ->as('admin.')
            ->group(base_path('routes/admin.php'));

    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes(): void
    {
        Route::prefix('api')
            ->middleware(['api', 'cors', ApiLocaleMiddleware::class])
            ->namespace('Src\Domain\Api\Controllers')
            ->as('api.')
            ->group(base_path('routes/api.php'));
    }
}
