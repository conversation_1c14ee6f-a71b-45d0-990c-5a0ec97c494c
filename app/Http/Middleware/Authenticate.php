<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        $path = $request->path();
        $base_path = explode('/', $path)[0];
        if (!$request->expectsJson()) {
            if ($base_path === 'admin') {
                return route('admin.auth.show');
            }
        }
    }
}
