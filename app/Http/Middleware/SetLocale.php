<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        if (session()->has('locale')) {
            app()->setLocale(session('locale'));
        } elseif ($request->hasCookie('locale')) {
            app()->setLocale($request->cookie('locale'));
        } else {
            app()->setLocale(env('APP_LOCALE', 'jp'));
        }

        return $next($request);
    }
}
