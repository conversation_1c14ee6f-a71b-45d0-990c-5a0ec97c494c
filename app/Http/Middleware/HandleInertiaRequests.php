<?php

namespace App\Http\Middleware;

use Auth;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Src\Domain\Admin\Models\Account\AccountDetail;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return array_merge(parent::share($request), [
            'author' => function () use ($request) {
                $author = Auth::guard('admin')->user();
                return $author ? (new AccountDetail($author))->toComponent() : null;
            },
            'flash' => function () use ($request) {
                return [
                    'success' => $request->session()->get('success'),
                    'error' => $request->session()->get('error'),
                ];
            },
            'translations' => function () {
                $locale = app()->getLocale();
                $translations = [];

                $getTranslations = function ($dir, $prefix = '') use (&$getTranslations, &$translations) {
                    $files = glob($dir . '/*');

                    foreach ($files as $file) {
                        if (is_dir($file)) {
                            $dirName = basename($file);
                            $getTranslations($file, $prefix . $dirName . '/');
                        } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                            $key = $prefix . basename($file, '.php');
                            $translations[$key] = require $file;
                        }
                    }
                };

                $getTranslations(base_path('lang/' . $locale));

                return $translations;
            }
        ]);
    }
}
