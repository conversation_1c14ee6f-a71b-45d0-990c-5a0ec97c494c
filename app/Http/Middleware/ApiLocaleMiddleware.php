<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiLocaleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $request->header('Accept-Language');

        if ($locale && in_array($locale, ['en', 'id', 'jp', 'mm', 'ne', 'vi'])) {
            app()->setLocale($locale);
        } else {
            app()->setLocale(env('APP_LOCALE', 'jp'));
        }

        return $next($request);
    }
}
