<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * Class Notification
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $notification_id
 * @property int $user_id
 * @property boolean $is_read
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property Notification $notification
 * @property User $user
 */
class NotificationUser extends Pivot
{
    protected $table = 't_notification_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'notification_id',
        'user_id',
        'is_read'
    ];

    protected $casts = [
        'notification_id' => 'integer',
        'user_id' => 'integer',
        'is_read' => 'boolean'
    ];
}
