<?php

namespace App\Eloquent;

use Illuminate\Support\Carbon;

/**
 * Class StorageFile
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property string $approval_status
 * @property int $front_card_id
 * @property int $back_card_id
 * @property string $period_type
 * @property string $school_name
 * @property int $front_identification_id
 * @property int $back_identification_id
 * @property string|Carbon $identification_expired_at
 * @property string|Carbon $period_of_stay
 * @property string|Carbon $period_expire_at
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class TmpResidenceCard extends Model
{
    protected $table = 't_user_tmp_residence_cards';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'approval_status',
        'front_card_id',
        'back_card_id',
        'period_type',
        'school_name',
        'front_identification_id',
        'back_identification_id',
        'identification_expired_at',
        'period_of_stay',
        'period_expire_at',
    ];
}
