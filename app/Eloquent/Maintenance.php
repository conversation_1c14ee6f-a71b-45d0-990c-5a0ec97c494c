<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Maintenance
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $title
 * @property string $message
 * @property boolean $is_active
 * @property string|Carbon $start_at
 * @property string|Carbon $end_at
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Maintenance extends Model
{
    use SoftDeletes;

    protected $table = 't_maintenances';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'title',
        'message',
        'is_active',
        'start_at',
        'end_at'
    ];
}
