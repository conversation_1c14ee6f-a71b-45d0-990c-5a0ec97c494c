<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class JobCategory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $bank_code
 * @property string $branch_code
 * @property string $name
 * @property string $name_kana
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class BankBranch extends Model
{
    use SoftDeletes;

    protected $table = 'm_bank_branches';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'bank_code',
        'branch_code',
        'name',
        'name_kana',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'bank_code' => 'string',
        'branch_code' => 'string',
        'name' => 'string',
        'name_kana' => 'string',
    ];
}
