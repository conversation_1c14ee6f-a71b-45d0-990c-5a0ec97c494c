<?php

namespace App\Eloquent;

use App\Eloquent\Concerns\ResidenceCard\HasRelationship;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class StorageFile
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property int $front_card_id
 * @property int $back_card_id
 * @property string $period_type
 * @property string $school_name
 * @property int $front_identification_id
 * @property int $back_identification_id
 * @property string|Carbon $identification_expired_at
 * @property string|Carbon $period_of_stay
 * @property string|Carbon $period_expire_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property StorageFile $frontCard
 * @property StorageFile $backCard
 * @property StorageFile $identification
 * @property StorageFile $frontIdentification
 * @property StorageFile $backIdentification
 */
class ResidenceCard extends Model
{
    use SoftDeletes, HasRelationship;

    protected $table = 't_user_residence_cards';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'front_card_id',
        'back_card_id',
        'period_type',
        'school_name',
        'front_identification_id',
        'back_identification_id',
        'identification_expired_at',
        'period_of_stay',
        'period_expire_at',
    ];
}
