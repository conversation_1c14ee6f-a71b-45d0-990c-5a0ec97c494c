<?php

namespace App\Eloquent;

use Carbon\Carbon;

/**
 * User Password Reset
 * @package App\Eloquent
 *
 * @property string $email
 * @property string $token
 * @property Carbon $expired_at
 */
class UserResetPassword extends Model
{
    protected $table = 't_user_reset_passwords';

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'email',
        'token',
        'expired_at',
    ];
}
