<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Prefecture
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $zip_code
 * @property string $name
 * @property string $address
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Station extends Model
{
    use SoftDeletes;

    protected $table = 'm_stations';
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'zip_code',
        'name',
        'address',
    ];

}
