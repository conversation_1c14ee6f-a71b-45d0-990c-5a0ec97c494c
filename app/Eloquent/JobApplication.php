<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class JobApplication
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $job_id
 * @property int $user_id
 * @property string $approval_status
 * @property int $resume_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class JobApplication extends Model
{
    use SoftDeletes;

    protected $table = 't_job_applications';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'job_id',
        'user_id',
        'approval_status',
        'resume_id',
    ];
}
