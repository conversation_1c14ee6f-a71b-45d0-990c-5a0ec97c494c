<?php

namespace App\Eloquent;

use Database\Factories\JobCategoryFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class JobCategory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class JobCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'm_job_categories';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'code',
        'name',
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return JobCategoryFactory::new();
    }
}
