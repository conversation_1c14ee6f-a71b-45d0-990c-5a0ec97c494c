<?php

namespace App\Eloquent;

use DB;
use Illuminate\Database\Eloquent\Model as BaseModel;
use App\Exceptions\ErrorException;

/**
 * Class Model
 * @package App\Eloquent
 */
class Model extends BaseModel
{

    /**
     * UserEnum exposed observable events.
     *
     * These are extra user-defined events observers may subscribe to.
     *
     * @var array
     */
    protected $observables = [
        'activating',
        'activated',
    ];

    /**
     * createOrThrow
     *
     * @param array $options
     * @return Model|\Illuminate\Database\Eloquent\Builder|BaseModel
     * @throws ErrorException
     */
    public function createOrThrow(array $options = [])
    {
        $model = $this->newModelQuery()->create($options);
        if (!$model) {
            throw new ErrorException(sprintf('save %s fails.', $this->table));
        }
        return $model;
    }

    /**
     * １件更新する。
     * 失敗した場合は例外を投げる
     *
     * @param array $options
     * @throws ErrorException
     */
    public function updateOrThrow(array $options = []): void
    {
        if (!$this->update($options)) {
            throw new ErrorException(sprintf('save %s fails.', $this->table));
        }
    }

    /**
     * saveOrThrow
     *
     * @param array $options
     * @throws ErrorException
     */
    public function saveOrThrow(array $options = []): void
    {
        if (!$this->save($options)) {
            throw new ErrorException(sprintf('save %s failed.', $this->table));
        }
    }

    /**
     * scopeSort
     *
     * @param $query
     * @param $key
     * @param $direction
     * @return mixed
     */
    public function scopeSort($query, $key, $direction)
    {
        return ($this->primaryKey === $key)
            ? $query->orderBy($key, $direction)
            : $query->orderBy($key, $direction)->orderBy($this->primaryKey, $direction);
    }


    public static function queryModel() {
        return static::query()->getModel();
    }
    public static function modelQuery() {
        return static::getModel()->newQuery();
    }

    /**
     * Bulk update
     *
     * @param array $values
     * @param string $index
     * @return bool
     */
    public function bulkUpdate(array $values, string $index = 'id'): bool
    {
        $final = [];
        $ids = [];

        if (\count($values) === 0) {
            return false;
        }

        foreach ($values as $key => $val) {
            $ids[] = $val[$index];
            foreach (array_keys($val) as $field) {
                if ($field !== $index) {
                    $value = ($val[$field] === null ? 'NULL' : '"' . $this->mysqlEscape($val[$field]) . '"');
                    $final[$field][] = 'WHEN `' . $index . '` = "' . $val[$index] . '" THEN ' . $value . ' ';
                }
            }
        }

        $cases = '';
        foreach ($final as $key => $value) {
            $cases .= '`' . $key . '` = (CASE ' . implode("\n", $value) . "\n" . 'ELSE `' . $key . '` END), ';
        }

        $query = "UPDATE `{$this->getTable()}` SET " . substr($cases, 0, -2) . " WHERE `$index` IN(" . implode(',', $ids) . ");";

        return DB::statement($query, [$index]);
    }

    /**
     * Escape mysql
     *
     * @param $inp
     * @return array|mixed
     */
    private function mysqlEscape($inp)
    {
        if (\is_array($inp)) {
            return array_map(__METHOD__, $inp);
        }

        if (!empty($inp) && \is_string($inp)) {
            return str_replace(['\\', "\0", "\n", "\r", "'", '"', "\x1a"], ['\\\\', '\\0', '\\n', '\\r', "\\'", '\\"', '\\Z'], $inp);
        }

        return $inp;
    }

    public static function getTableName()
    {
        return with(new static)->getTable();
    }

}
