<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Prefecture
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $zip_code
 * @property string $prefecture
 * @property string $city
 * @property string $town_area
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Prefecture extends Model
{
    use SoftDeletes;

    protected $table = 'm_prefectures';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'zip_code',
        'prefecture',
        'city',
        'town_area',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'zip_code' => 'integer',
        'prefecture' => 'string',
        'city' => 'string',
        'town_area' => 'string',
    ];
}
