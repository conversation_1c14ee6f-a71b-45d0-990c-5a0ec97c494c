<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class EmailTemplate
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $mail_type
 * @property string $subject
 * @property string $body
 * @property array $placeholders
 * @property boolean $is_active
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class EmailTemplate extends Model
{
    use SoftDeletes;

    protected $table = 't_email_templates';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'mail_type',
        'subject',
        'body',
        'placeholders',
        'is_active'
    ];

    protected $casts = [
        'placeholders' => 'array',
    ];
}
