<?php

namespace App\Eloquent\Concerns\User;

use App\Eloquent\Job;
use App\Eloquent\Notification;
use App\Eloquent\NotificationUser;
use App\Eloquent\ResidenceCard;
use App\Eloquent\StorageFile;
use App\Eloquent\TmpResidenceCard;
use App\Eloquent\UserBank;
use App\Eloquent\UserPointHistory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HasRelationship
{
    /**
     * @return BelongsTo
     */
    public function avatar(): BelongsTo
    {
        return $this->belongsTo(StorageFile::class, 'avatar_id');
    }

    /**
     * @return BelongsTo
     */
    public function healthCertificate(): BelongsTo
    {
        return $this->belongsTo(StorageFile::class, 'health_certificate_id');
    }

    /**
     * @return BelongsTo
     */
    public function passport(): BelongsTo
    {
        return $this->belongsTo(StorageFile::class, 'passport_image_id');
    }

    /**
     * @return HasOne
     */
    public function userBank(): HasOne
    {
        return $this->hasOne(UserBank::class, 'user_id');
    }

    /**
     * @return HasOne
     */
    public function residenceCard(): HasOne
    {
        return $this->hasOne(ResidenceCard::class, 'user_id');
    }

    /**
     * @return HasOne
     */
    public function tmpResidenceCards(): HasOne
    {
        return $this->hasOne(TmpResidenceCard::class, 'user_id');
    }

    public function tNotifications(): BelongsToMany
    {
        return $this->belongsToMany(Notification::class, 't_notification_users', 'user_id', 'notification_id')
            ->withPivot('is_read')
            ->withTimestamps();
    }

    public function jobs(): BelongsToMany
    {
        return $this->belongsToMany(Job::class, 't_job_applications')->withPivot('approval_status');
    }

    public function pointHistory(): HasMany
    {
        return $this->hasMany(UserPointHistory::class);
    }

    public function jobFavorites(): BelongsToMany
    {
        return $this->belongsToMany(Job::class, 't_user_favorites');
    }
}
