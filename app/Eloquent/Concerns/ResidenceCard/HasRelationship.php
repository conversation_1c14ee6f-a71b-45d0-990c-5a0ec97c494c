<?php

namespace App\Eloquent\Concerns\ResidenceCard;

use App\Eloquent\StorageFile;

trait HasRelationship
{
    public function frontCard()
    {
        return $this->belongsTo(StorageFile::class, 'front_card_id');
    }

    public function backCard()
    {
        return $this->belongsTo(StorageFile::class, 'back_card_id');
    }

    public function identification()
    {
        return $this->belongsTo(StorageFile::class, 'identification_id');
    }

    public function frontIdentification()
    {
        return $this->belongsTo(StorageFile::class, 'front_identification_id');
    }

    public function backIdentification()
    {
        return $this->belongsTo(StorageFile::class, 'back_identification_id');
    }
}
