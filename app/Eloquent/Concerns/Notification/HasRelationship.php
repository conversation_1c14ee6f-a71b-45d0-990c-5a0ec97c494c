<?php

namespace App\Eloquent\Concerns\Notification;

use App\Eloquent\Job as TJOb;
use App\Eloquent\NotificationUser;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait HasRelationship
{
    public function job()
    {
        return $this->belongsTo(TJOb::class, 'job_id');
    }

    public function notificationUsers()
    {
        return $this->hasMany(NotificationUser::class, 'notification_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 't_user_notification_pivot', 'user_notification_id', 'user_id')
            ->withPivot('is_read_detail')
            ->withTimestamps();
    }
}
