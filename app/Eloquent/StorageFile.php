<?php

namespace App\Eloquent;

use Database\Factories\StorageFileFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class StorageFile
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $file_div
 * @property string $file_path
 * @property string $file_type
 * @property int $file_size
 * @property string $file_url
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class StorageFile extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 't_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'file_div',
        'file_path',
        'file_type',
        'file_size',
        'file_url'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'file_div' => 'integer',
        'file_path' => 'string',
        'file_type' => 'string',
        'file_size' => 'integer',
        'file_url' => 'string'
    ];

    protected static function newFactory()
    {
        return StorageFileFactory::new();
    }
}
