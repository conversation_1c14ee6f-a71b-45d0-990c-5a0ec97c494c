<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Class JobFavorite
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $job_id
 * @property int $user_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserJobFavorite extends Model
{

    protected $table = 't_user_job_favorites';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'job_id',
        'user_id',
    ];
}
