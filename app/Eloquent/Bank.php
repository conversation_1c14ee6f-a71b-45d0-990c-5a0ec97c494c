<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class JobCategory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $code
 * @property string $name
 * @property string $name_kana
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property BankBranch[] $branches
 */
class Bank extends Model
{
    use SoftDeletes;

    protected $table = 'm_banks';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'code',
        'name',
        'name_kana'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'code' => 'string',
        'name' => 'string',
        'name_kana' => 'string',
    ];

    /**
     * Get the branches associated with the bank.
     *
     * @return HasMany
     */
    public function branches(): HasMany
    {
        return $this->hasMany(BankBranch::class, 'bank_code', 'code');
    }
}
