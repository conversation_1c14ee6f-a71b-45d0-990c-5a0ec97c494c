<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class UserPointHistory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property int $point
 * @property int $new_point
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserPointHistory extends Model
{
    use SoftDeletes;

    protected $table = 't_user_point_histories';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'point',
        'new_point'
    ];
}
