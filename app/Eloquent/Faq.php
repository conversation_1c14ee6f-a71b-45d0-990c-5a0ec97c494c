<?php

namespace App\Eloquent;

use App\Eloquent\Concerns\Faq\HasRelationship;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Faq
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $question
 * @property string $answer
 * @property string $type
 * @property array $file_ids
 * @property boolean $is_public
 * @property int $account_id
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Faq extends Model
{
    use SoftDeletes;

    protected $table = 't_faqs';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'question',
        'answer',
        'type',
        'is_public',
        'file_ids',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'file_ids' => 'array',
    ];

    public function scopeIsPublic()
    {
        return $this->where('is_public', true);
    }
}
