<?php

namespace App\Eloquent;

use Database\Factories\MGroupFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class JobCategory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $wss_code
 * @property string $company_name
 * @property string $site_name
 * @property string $category_1
 * @property string $category_2
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Group extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'm_groups';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'wss_code',
        'company_name',
        'site_name',
        'category_1',
        'category_2',
    ];
}
