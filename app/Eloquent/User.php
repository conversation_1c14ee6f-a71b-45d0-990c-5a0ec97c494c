<?php

namespace App\Eloquent;

use App\Eloquent\Concerns\User\HasRelationship;
use Carbon\Carbon;
use Database\Factories\UserFactory;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;

/**
 * Class User
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $code
 * @property array $group_ids
 * @property string $name
 * @property string $email
 * @property string $password
 * @property int $avatar_id
 * @property int $health_certificate_id
 * @property int $user_status
 * @property string $name_kana
 * @property string $name_kanji
 * @property string $phone_number
 * @property int $gender
 * @property string $birthday
 * @property string $nationality
 * @property boolean $has_certificate
 * @property string $japanese_level
 * @property string $arrival_date
 * @property string $coming_date
 * @property string $zip_code
 * @property string $prefecture
 * @property string $street_address
 * @property string $town_address
 * @property string $train_station_name
 * @property string $emergency_name
 * @property string $emergency_relation
 * @property string $emergency_phone_number
 * @property int $bank_type
 * @property string $email_verification_token
 * @property Carbon|null $email_verification_at
 * @property Carbon|null $identification_at
 * @property int $job_participation_count
 * @property boolean $is_recommended
 * @property boolean $is_disable
 * @property Carbon|null $disable_until_at
 * @property int $point
 * @property int $passport_image_id
 * @property string $passport_number
 * @property Carbon $passport_expired_at
 * @property int $number_unread_notification
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $deleted_at
 *
 * @property StorageFile $avatar
 * @property StorageFile $healthCertificate
 * @property StorageFile $passport
 * @property UserBank $userBank
 * @property ResidenceCard $residenceCard
 * @property TmpResidenceCard[] $tmpResidenceCards
 * @property Notification[] $tNotifications
 */
class User extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, HasApiTokens, Notifiable, SoftDeletes, HasFactory, HasRelationship, MustVerifyEmail;

    protected $table = 't_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'code',
        'group_ids',
        'name',
        'email',
        'password',
        'avatar_id',
        'health_certificate_id',
        'user_status',
        'name_kana',
        'name_kanji',
        'phone_number',
        'gender',
        'birthday',
        'nationality',
        'has_certificate',
        'japanese_level',
        'arrival_date',
        'zip_code',
        'prefecture',
        'street_address',
        'town_address',
        'train_station_name',
        'emergency_name',
        'emergency_relation',
        'emergency_phone_number',
        'bank_type',
        'email_verification_token',
        'email_verification_at',
        'identification_at',
        'job_participation_count',
        'is_recommended',
        'is_disable',
        'point',
        'passport_image_id',
        'passport_number',
        'passport_expired_at',
        'number_unread_notification',
        'disable_until_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected $casts = [
        'code' => 'string',
        'avatar_id' => 'integer',
        'group_ids' => 'array'
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return UserFactory::new();
    }

    public function hasVerifiedEmail(): bool
    {
        return ! is_null($this->email_verification_at);
    }

    public function markEmailAsVerified(): bool
    {
        return $this->forceFill([
            'email_verification_at' => $this->freshTimestamp(),
            'email_verification_token' => null
        ])->save();
    }

    /**
     * @return int|null
     */
    public function getAgeAttribute(): ?int
    {
        try {
            if ($this->birthday === null) {
                return null;
            }
            $year = (new \DateTime($this->birthday))->format('Y');
            return now()->subYears($year)->format('Y');
        } catch (\Exception $e) {
            return null;
        }
    }
}
