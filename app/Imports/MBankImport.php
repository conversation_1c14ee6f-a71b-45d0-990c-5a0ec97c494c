<?php

namespace App\Imports;

use App\Eloquent\Bank;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MBankImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if ($row['code'] && $row['bank_name_kana'] && $row['bank_name']) {
                Bank::queryModel()->create([
                    'code' => $row['code'],
                    'name' => $row['bank_name'],
                    'name_kana' => $row['bank_name_kana'],
                ]);
            }
        }
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
