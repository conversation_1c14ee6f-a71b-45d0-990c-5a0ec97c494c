<?php

namespace App\Imports;

use App\Eloquent\Group;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MGroupImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if ($row['wss_code']) {
                Group::queryModel()->create([
                    'wss_code' => $row['wss_code'],
                    'company_name' => $row['company_name'],
                    'site_name' => $row['site_name'],
                ]);
            }
        }
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
