<?php

namespace App\Imports;

use App\Eloquent\BankBranch;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MBankBranchImport implements ToCollection, WithHeadingRow, WithChunkReading
{
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if ($row['bank_code'] && $row['branch_code'] && $row['branch_name_kana'] && $row['branch_name']) {
                BankBranch::queryModel()->create([
                    'bank_code' => $row['bank_code'],
                    'branch_code' => $row['branch_code'],
                    'name' => $row['branch_name'],
                    'name_kana' => $row['branch_name_kana'],
                ]);
            }
        }
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
