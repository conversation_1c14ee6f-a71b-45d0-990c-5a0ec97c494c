<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VerifyEmailRegister extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public string $token;

    public string $name;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $token, string $name)
    {
        $this->token = $token;
        $this->name = $name;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->view('mails.verify-email')
            ->subject('Recruit - Verify Email Address')
            ->with([
                'name' => $this->name,
                'url' => $this->verificationUrl()
            ]);
    }

    protected function verificationUrl(): string
    {
        return config('app.frontend_url').'/verify-email?token='.$this->token;
    }
}
