<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Src\Enums\ResultCode;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

/**
 * Class Handler
 * @package App\Exceptions
 */
class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @return void
     *
     * @throws Throwable
     */
    public function report(Throwable $e)
    {
        parent::report($e);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param Throwable $e
     * @return JsonResponse|Response
     * @throws Throwable
     */
    public function render($request, Throwable $e)
    {
        if ($request->is('api/*')) {
            $status_code = 500;

            if ($e instanceof ModelNotFoundException) {
                return json_response(ResultCode::NOT_FOUND, null,  [
                    'message' => 'Not Found'
                ]);
            }

            if ($e instanceof AuthenticationException) {
                return json_response(ResultCode::TOKEN_INVALID, null,  [
                    'message' => 'Unauthorized'
                ]);
            }

            if ($e instanceof ValidationException) {

                return json_response(ResultCode::ERROR_INPUT, null,  [
                    'message' =>  'The given data was invalid.',
                    'errors' => $e->errors()
                ]);
            }

            if ($e instanceof APIRuntimeException) {
                return json_response($e->getCode(), null, ['message' => $e->getMessage()]);
            }

            if (method_exists($e, 'getStatusCode')) {
                $status_code = $e->getStatusCode();
            }

            $response['code'] = $status_code;
            switch ($status_code) {
                case 404:
                    $response['message'] = 'Not Found';
                    break;
                case 403:
                    $response['message'] = 'Forbidden';
                    break;
                default:
                    $response['message'] = $e->getMessage();

                    break;
            }

            return json_response(ResultCode::ERROR, null, $response);
        }

        return parent::render($request, $e);
    }
}
