<?php

namespace Src\Domain\Api\Docs;

use OpenApi\Attributes as OA;

#[OA\Tag(
    name: 'Job',
    description: 'Job endpoints'
)]
class JobDoc
{
    #[OA\Get(
        path: '/job',
        summary: 'Get job information',
        tags: ['Job'],
        parameters: [
            new OA\Parameter(
                name: 'category_id',
                description: 'Search by category',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'number',
                )
            ),
            new OA\Parameter(
                name: 'type',
                description: 'Search by type',
                in: 'query',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/ListJobResponse')
            ),
        ]
    )]
    public function getJob() {}

    #[OA\Get(
        path: '/job/{id}/show',
        summary: 'Get detail job information',
        security: [['passport' => []]],
        tags: ['Job'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Job id',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'number',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/JobDetailResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/AuthorizedResponse')
            )
        ]
    )]
    public function getJobDetail() {}

    #[OA\Get(
        path: '/job-user-applied',
        summary: 'Get job user has applied',
        security: [['passport' => []]],
        tags: ['Job'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/ListJobResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/AuthorizedResponse')
            )
        ]
    )]
    public function listJobUserApplied() {}

    #[OA\Post(
        path: '/cancel-job-applied',
        summary: 'Get job user has applied',
        security: [['passport' => []]],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['job_id'],
                    properties: [
                        new OA\Property(property: 'job_id', type: 'number'),
                    ]
                )
            )
        ),
        tags: ['Job'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/CancelJobAppliedResponseSuccess')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/AuthorizedResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function cancelJobApplied() {}

    #[OA\Post(
        path: '/job/{id}/application',
        summary: 'Job application',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    required: ['user_id', 'resume_id'],
                    properties: [
                        new OA\Property(property: 'user_id', type: 'string'),
                        new OA\Property(property: 'resume_id', description: 'Image file (jpeg,png,jpg) max 2MB', type: 'string')
                    ]
                )
            )
        ),
        tags: ['Job'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Login successful',
                content: new OA\JsonContent(ref: '#/components/schemas/SuccessResponse')
            ),
            new OA\Response(
                response: 401,
                description: 'Unauthorized',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function jobApplication() {}

    #[OA\Post(
        path: '/job/{id}/favorite',
        summary: 'Favorite the job"',
        tags: ['Job'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Job id',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'number',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/JobDetailResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function jobFavorite() {}

    #[OA\Delete(
        path: '/job/{id}/favorite',
        summary: 'Favorite the job"',
        tags: ['Job'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Job id',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'number',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/JobDetailResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function jobRemoveFavorite() {}
}
