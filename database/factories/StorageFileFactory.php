<?php

namespace Database\Factories;

use App\Eloquent\StorageFile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Eloquent\StorageFile>
 */
class StorageFileFactory extends Factory
{
    protected $model = StorageFile::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'file_div' => 1,
            'file_path' => 'https://picsum.photos/seed/' . uniqid() . '/640/480',
            'file_size' => $this->faker->numberBetween(10,100),
            'file_type' => $this->faker->randomElement(['image/jpeg','image/png','image/gif']),
            'file_url' => 'https://picsum.photos/seed/' . uniqid() . '/640/480',
        ];
    }
}
