<?php

namespace Database\Factories;

use App\Eloquent\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Src\Enums\AccountRole;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Model>
 */
class AccountFactory extends Factory
{
    protected $model = Account::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name,
            'login_id' => fake()->userName,
            'email' => fake()->unique()->safeEmail(),
            'password' => bcrypt('123456'),
            'role_div' => fake()->randomElement(AccountRole::asArray()),
        ];
    }
}
