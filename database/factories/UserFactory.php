<?php

namespace Database\Factories;

use App\Eloquent\Relationship;
use App\Eloquent\StorageFile;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Eloquent\User;
use Src\Enums\CertificateLevel;
use Src\Enums\EmergencyRelation;
use Src\Enums\Gender;
use Src\Enums\UserBankType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Eloquent\User>
 */
class UserFactory extends Factory
{
    protected $model = User::class;

    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->randomNumber(6, true),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => $this->faker->password(),
            'name_kana' => $this->faker->name(),
            'name_kanji' => $this->faker->name(),
            'phone_number' => $this->faker->numerify('###########'),
            'gender' => $this->faker->randomElement(Gender::asArray()),
            'birthday' => $this->faker->date(),
            'nationality' => $this->faker->countryCode(),
            'has_certificate' => $this->faker->boolean(),
            'japanese_level' => $this->faker->randomElement(CertificateLevel::asArray()),
            'arrival_date' => $this->faker->date(),
            'zip_code' =>  $this->faker->randomNumber(2, true) .'-'. $this->faker->randomNumber(4, true),
            'prefecture' => $this->faker->word(),
            'town_address' => $this->faker->address(),
            'street_address' => $this->faker->streetAddress(),
            'train_station_name' => $this->faker->streetName(),
            'bank_type' => UserBankType::CASH,
            'avatar_id' => StorageFile::query()->inRandomOrder()->first()?->id ?? StorageFile::factory(),
            'health_certificate_id' => StorageFile::query()->inRandomOrder()->first()?->id ?? StorageFile::factory(),
            'passport_image_id' => StorageFile::query()->inRandomOrder()->first()?->id ?? StorageFile::factory(),
            'passport_number' => $this->faker->numerify('#######'),
            'passport_expired_at' => $this->faker->date()
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verification_at' => null,
            'identification_at' => null,
            'is_disable' => false,
        ]);
    }
}
