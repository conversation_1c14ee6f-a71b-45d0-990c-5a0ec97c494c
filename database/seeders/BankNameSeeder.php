<?php

namespace Database\Seeders;

use App\Imports\MBankBranchImport;
use App\Imports\MBankImport;
use DB;
use Maatwebsite\Excel\Facades\Excel;
use Storage;
use Illuminate\Database\Seeder;

class BankNameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('m_banks')->truncate();
        DB::table('m_bank_branches')->truncate();
        $file_bank = Storage::disk('local')->path('bank.xlsx');
        $file_branch = Storage::disk('local')->path('bank_branch.xlsx');
        Excel::import(new MBankImport(), $file_bank);
        Excel::import(new MBankBranchImport(), $file_branch);
    }
}
