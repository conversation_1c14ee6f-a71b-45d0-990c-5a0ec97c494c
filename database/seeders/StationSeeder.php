<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('m_stations')->truncate();
        $csv_file = \Storage::disk('local')->path('stations.csv');
        $file = fopen($csv_file, 'r');

        // Set input encoding
        mb_internal_encoding('UTF-8');

        $chunk = [];
        $chunkSize = 1000;
        $lineNumber = 0;
        $stationsName = [];

        while (($data = fgetcsv($file)) !== false) {
            $lineNumber++;
            if ($lineNumber === 1) {
                continue;
            }
            $stationName = mb_convert_encoding($data[4], 'UTF-8', 'SJIS');
            if (!in_array($stationName, $stationsName)) {
                $chunk[] = [
                    'zip_code' =>  str_replace('-', '', mb_convert_encoding($data[13], 'UTF-8', 'SJIS-win')),
                    'name' => mb_convert_encoding($data[4], 'UTF-8', 'SJIS'),
                    'address' => mb_convert_encoding($data[14], 'UTF-8', 'SJIS'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            $stationsName[] = mb_convert_encoding($data[4], 'UTF-8', 'SJIS');
            if (count($chunk) >= $chunkSize) {
                DB::table('m_stations')->insert($chunk);
                $chunk = [];
            }
        }

        if (!empty($chunk)) {
            DB::table('m_stations')->insert($chunk);
        }

        fclose($file);
    }
}
