<?php

namespace Database\Seeders;

use App\Eloquent\Prefecture;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PrefectureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('m_prefectures')->truncate();
        $csv_file = \Storage::disk('local')->path('prefecture.csv');
        $file = fopen($csv_file, 'r');

        // Set input encoding
        mb_internal_encoding('UTF-8');

        $chunk = [];
        $chunkSize = 1000;

        while (($data = fgetcsv($file)) !== false) {
            $chunk[] = [
                'zip_code' => mb_convert_encoding($data[2], 'UTF-8', 'SJIS'),
                'prefecture' => mb_convert_encoding($data[6], 'UTF-8', 'SJIS'),
                'city' => mb_convert_encoding($data[7], 'UTF-8', 'SJIS'),
                'town_area' => mb_convert_encoding($data[8], 'UTF-8', 'SJIS'),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if (count($chunk) >= $chunkSize) {
                DB::table('m_prefectures')->insert($chunk);
                $chunk = [];
            }
        }

        if (!empty($chunk)) {
            DB::table('m_prefectures')->insert($chunk);
        }

        fclose($file);
    }
}
