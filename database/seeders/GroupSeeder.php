<?php

namespace Database\Seeders;

use App\Imports\MGroupImport;
use Illuminate\Database\Seeder;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class GroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $file = Storage::disk('local')->path('group.xlsx');
        Excel::import(new MGroupImport(), $file);
    }
}
