<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_jobs', function (Blueprint $table) {
            $table->string('employer_name', 100)->after('employer_email')->nullable();
            $table->string('employer_phone_number', 11)->after('employer_name')->nullable();
            $table->boolean('is_filled')->after('expired_at')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('t_jobs', function (Blueprint $table) {
            //
        });
    }
};
