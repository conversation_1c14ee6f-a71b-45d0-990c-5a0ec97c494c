<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Src\Enums\UserStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_users', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('code', 6)->unique('code_UNIQUE');
            $table->string('email', 50)->unique('email_UNIQUE')->index();
            $table->string('password');
            $table->string('name', 100);
            $table->string('name_kana', 100)->nullable();
            $table->string('name_kanji', 100)->nullable();
            $table->integer('avatar_id')->unsigned();
            $table->integer('health_certificate_id')->unsigned();
            $table->string('user_status', 20)->default(UserStatus::INITIAL);
            $table->string('phone_number', 11);
            $table->string('gender', 10);
            $table->date('birthday');
            $table->string('nationality', 5);
            $table->boolean('has_certificate');
            $table->string('japanese_level', 10);
            $table->date('arrival_date');
            $table->string('zip_code', 7);
            $table->string('prefecture', 100);
            $table->string('street_address', 255);
            $table->string('town_address', 255)->nullable();
            $table->string('train_station_name', 100);
            $table->string('emergency_name', 100)->nullable();
            $table->string('emergency_relation', 20)->nullable();
            $table->string('emergency_phone_number', 11)->nullable();
            $table->string('bank_type', 20);
            $table->integer('point')->nullable()->default(0);
            $table->integer('passport_image_id')->unsigned();
            $table->string('passport_number', 40);
            $table->date('passport_expired_at');
            $table->integer('number_unread_notification')->nullable()->default(0);
            $table->string('email_verification_token')->nullable();
            $table->timestamp('email_verification_at')->nullable();
            $table->timestamp('identification_at')->nullable();
            $table->boolean('is_disable')->default(false);
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('avatar_id')->references('id')->on('t_files');
            $table->foreign('health_certificate_id')->references('id')->on('t_files');
            $table->foreign('passport_image_id')->references('id')->on('t_files');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_users');
    }
};
