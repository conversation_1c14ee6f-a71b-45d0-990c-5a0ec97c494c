<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_user_residence_cards', function (Blueprint $table) {
            $table->dropColumn('period_expire_at');
        });
        Schema::table('t_user_tmp_residence_cards', function (Blueprint $table) {
            $table->dropColumn('period_expire_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('t_user_residence_cards', function (Blueprint $table) {
            //
        });
    }
};
