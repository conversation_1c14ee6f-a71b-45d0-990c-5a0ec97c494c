<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_job_images', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->integer('job_id')->unsigned();
            $table->integer('image_id')->unsigned();
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('job_id')->references('id')->on('t_jobs');
            $table->foreign('image_id')->references('id')->on('t_files');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_job_images');
    }
};
