<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_job_applications', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->integer('job_id')->unsigned();
            $table->integer('user_id')->unsigned();
            $table->string('approval_status', 20);
            $table->integer('resume_id')->unsigned()->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('job_id')->references('id')->on('t_jobs');
            $table->foreign('user_id')->references('id')->on('t_users');
            $table->foreign('resume_id')->references('id')->on('t_files')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_job_applications');
    }
};
