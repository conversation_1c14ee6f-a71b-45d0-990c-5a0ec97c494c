<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_user_residence_cards', function (Blueprint $table) {
            $table->string('period_type', 40)->change();
        });
        Schema::table('t_user_tmp_residence_cards', function (Blueprint $table) {
            $table->string('period_type', 40)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
