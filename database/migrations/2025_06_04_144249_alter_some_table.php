<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Src\Enums\PriorityLevel;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_accounts', function (Blueprint $table) {
            $table->dropColumn('login_id');
            $table->string('department', 100)->after('name');
        });
        Schema::table('t_faqs', function (Blueprint $table) {
            $table->json('file_ids')->nullable()->after('answer');
        });
        Schema::table('t_notifications', function (Blueprint $table) {
            $table->json('file_ids')->nullable()->after('body');
            $table->string('priority_level', 20)->default(PriorityLevel::MEDIUM)->after('file_ids');
        });
        Schema::table('t_users', function (Blueprint $table) {
            $table->integer('job_participation_count')->default(0)->after('identification_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
