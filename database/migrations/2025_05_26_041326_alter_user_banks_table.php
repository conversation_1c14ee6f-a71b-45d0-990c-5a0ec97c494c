<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_user_banks', function (Blueprint $table) {
            $table->renameColumn('bank_name', 'bank_code');
            $table->renameColumn('bank_branch', 'branch_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('t_user_banks', function (Blueprint $table) {
            $table->renameColumn('bank_code', 'bank_name');
            $table->renameColumn('branch_code', 'bank_branch');
        });
    }
};
