<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Src\Enums\ApprovalStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_accounts', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('login_id', 40)->unique('login_id_UNIQUE');
            $table->string('email', 50)->unique('email_UNIQUE');
            $table->string('password');
            $table->string('name', 100);
            $table->string('role_div', 20);
            $table->string('approval_status', 20)->default(ApprovalStatus::WAITING);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_accounts');
    }
};
