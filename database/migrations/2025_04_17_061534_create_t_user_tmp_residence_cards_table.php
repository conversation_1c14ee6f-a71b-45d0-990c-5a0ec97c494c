<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Src\Enums\ApprovalStatus;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_user_tmp_residence_cards', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->integer('user_id')->unsigned()->unique();
            $table->string('approval_status', 20)->default(ApprovalStatus::WAITING);
            $table->integer('front_card_id')->unsigned();
            $table->integer('back_card_id')->unsigned();
            $table->string('period_type', 20);
            $table->string('school_name', 100)->nullable();
            $table->integer('front_identification_id')->unsigned();
            $table->integer('back_identification_id')->unsigned()->nullable();
            $table->date('identification_expired_at')->nullable();
            $table->date('period_of_stay');
            $table->date('period_expire_at');
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('user_id')->references('id')->on('t_users');
            $table->foreign('front_card_id')->references('id')->on('t_files');
            $table->foreign('back_card_id')->references('id')->on('t_files');
            $table->foreign('front_identification_id')->references('id')->on('t_files');
            $table->foreign('back_identification_id')->references('id')->on('t_files')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_user_tmp_residence_cards');
    }
};
