<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('m_groups', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('name', 100)->unique();
            $table->softDeletes();
            $table->timestamps();
        });
        Schema::table('t_users', function (Blueprint $table) {
            $table->json('group_ids')->nullable()->after('code');
        });
        Schema::table('t_jobs', function (Blueprint $table) {
            $table->integer('group_id')->nullable()->after('id');

            $table->foreign('group_id')->references('id')->on('m_groups')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('m_groups');
        Schema::table('t_users', function (Blueprint $table) {
            $table->dropColumn('group_ids');
        });
        Schema::table('t_jobs', function (Blueprint $table) {
            $table->dropColumn('group_id');
        });
    }
};
