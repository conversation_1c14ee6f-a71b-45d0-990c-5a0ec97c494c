<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('m_prefectures', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('zip_code', 7)->index();
            $table->string('prefecture', 255);
            $table->string('city', 255);
            $table->string('town_area', 255);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('m_prefectures');
    }
};
