<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_user_point_histories', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->integer('user_id')->unsigned();
            $table->integer('point');
            $table->integer('new_point');
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('user_id')->references('id')->on('t_users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_user_point_histories');
    }
};
