<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_notifications', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('type', 20);
            $table->string('title', 1000);
            $table->text('body');
            $table->integer('job_id')->unsigned()->nullable();
            $table->timestamp('start_notification_at');
            $table->timestamp('end_notification_at')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('job_id')->references('id')->on('t_jobs')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_notifications');
    }
};
