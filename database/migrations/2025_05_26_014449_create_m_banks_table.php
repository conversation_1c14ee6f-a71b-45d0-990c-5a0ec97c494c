<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('m_banks', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('code', 10)->unique()->index();
            $table->string('name', 100)->index();
            $table->string('name_kana', 100);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('m_banks');
    }
};
