<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_users', function (Blueprint $table) {
            $table->boolean('is_recommended')->default(false)->after('identification_at');
        });
        Schema::table('t_jobs', function (Blueprint $table) {
            $table->boolean('is_recommended')->default(false)->after('expired_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('t_users', function (Blueprint $table) {
            $table->dropColumn('is_recommended');
        });
        Schema::table('t_jobs', function (Blueprint $table) {
            $table->dropColumn('is_recommended');
        });
    }
};
