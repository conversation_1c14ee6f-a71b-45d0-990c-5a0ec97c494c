<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_jobs', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->string('recruitment_type', 20);
            $table->string('employer_email', 50);
            $table->integer('category_id')->unsigned();
            $table->string('type', 20);
            $table->boolean('is_public')->default(false);
            $table->boolean('is_instant')->default(false);
            $table->integer('thumbnail_id')->unsigned();
            $table->string('title', 1000);
            $table->text('description');
            $table->text('benefits')->nullable();
            $table->time('time_start')->nullable();
            $table->time('time_end')->nullable();
            $table->integer('age')->nullable();
            $table->string('gender', 10)->nullable();
            $table->integer('quantity')->nullable();
            $table->string('certificate_level', 10);
            $table->string('prefecture', 255);
            $table->string('address', 255);
            $table->string('salary_type', 10);
            $table->integer('salary')->nullable();
            $table->string('travel_fee_type', 10);
            $table->integer('travel_fee')->nullable();
            $table->date('expired_at');
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('category_id')->references('id')->on('m_job_categories');
            $table->foreign('thumbnail_id')->references('id')->on('t_files');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_jobs');
    }
};
