<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_user_banks', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->integer('user_id')->unsigned()->unique();
            $table->integer('atm_image_id')->unsigned()->nullable();
            $table->string('bank_name', 100);
            $table->string('bank_branch', 100);
            $table->string('account_name', 100);
            $table->string('account_number', 45);
            $table->string('deposit_type', 20);
            $table->softDeletes();
            $table->timestamps();

            // Foreign key
            $table->foreign('user_id')->references('id')->on('t_users');
            $table->foreign('atm_image_id')->references('id')->on('t_files')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_user_banks');
    }
};
