<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('t_users', function (Blueprint $table) {
            $table->timestamp('disable_until_at')->nullable()->after('is_disable');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('t_users', function (Blueprint $table) {
            $table->dropColumn('disable_until_at');
        });
    }
};
