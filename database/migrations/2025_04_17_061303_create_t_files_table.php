<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('t_files', function (Blueprint $table) {
            $table->integer('id', true)->unsigned();
            $table->tinyInteger('file_div');
            $table->string('file_type', 45);
            $table->integer('file_size')->default(0);
            $table->string('file_path', 255);
            $table->string('file_url', 255);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('t_files');
    }
};
