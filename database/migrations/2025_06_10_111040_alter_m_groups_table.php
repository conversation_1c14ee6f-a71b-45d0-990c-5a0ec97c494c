<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('m_groups', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->string('wss_code', 10)->unique()->after('id');
            $table->string('company_name', 100)->after('wss_code');
            $table->string('site_name', 100)->after('company_name');
            $table->string('category_1', 40)->nullable()->after('site_name');
            $table->string('category_2', 40)->nullable()->after('category_1');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
