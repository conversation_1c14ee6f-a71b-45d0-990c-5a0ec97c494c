<?php

namespace Tests\Feature\Api;

use Src\Enums\CertificateLevel;
use Src\Enums\Gender;
use Src\Enums\ResidenceCardStatus;
use Src\Enums\ResultCode;
use Src\Enums\UserBankType;
use Tests\TestCase;
use Faker\Factory as Faker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class UserTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function test_user_can_register(): void
    {
        Storage::fake(config('filesystems.default'));
        $faker = Faker::create();
        $bank_type = $faker->randomElement(UserBankType::asArray());
        $period_type = $faker->randomElement(ResidenceCardStatus::PERIOD_TYPE);

        $data = [
            'name' => $faker->name(),
            'email' => $faker->unique()->safeEmail(),
            'password' => Str::random(10),
            'avatar' => UploadedFile::fake()->image(Str::random(10) . '.jpg'),
            'name_kana' => $faker->name(),
            'name_kanji' => $faker->name(),
            'health_certificate' => UploadedFile::fake()->image(Str::random(10) . '.jpg'),
            'phone_number' => Str::random(10),
            'gender' => $faker->randomElement(Gender::asArray()),
            'birthday' => $faker->date(),
            'national' => $faker->country(),
            'has_certificate' => $faker->randomElement([true, false]),
            'japanese_lever' => $faker->randomElement(CertificateLevel::asArray()),
            'coming_date' => $faker->date(),
            'zip_code' => $faker->randomNumber(2, true) .'-'. $faker->randomNumber(4, true),
            'prefecture' => $faker->word(),
            'street_address' => $faker->streetAddress(),
            'train_station' => $faker->streetName(),
            'emergency_name' => $faker->name(),
            'emergency_relation' => $faker->word(),
            'emergency_phone' => Str::random(10),
            'bank_type' => $bank_type,
            'front_card' => UploadedFile::fake()->image(Str::random(10) . '.jpg'),
            'back_card' => UploadedFile::fake()->image(Str::random(10) . '.jpg'),
            'period_type' => $period_type,
            'period_of_stay' => $faker->date(),
            'period_expire_at' => $faker->date(),
            'passport' => UploadedFile::fake()->image(Str::random(10) . '.jpg'),
        ];

        if ($bank_type == UserBankType::BANK) {
            $data['bank_name'] = $faker->name();
            $data['bank_branch'] = $faker->name();
            $data['account_name'] = $faker->name();
            $data['account_number'] = $faker->randomNumber();
        }

        if ($period_type == ResidenceCardStatus::PERIOD_TYPE['study_abroad']) {
            $data['school_name'] = $faker->name();
        }

        if ($period_type == ResidenceCardStatus::PERIOD_TYPE['designated_activities']) {
            $data['identification'] = UploadedFile::fake()->image(Str::random(10) . '.jpg');
        }

        $response = $this->postJson(route('api.sign-up'), $data);
        $response->assertJsonPath('result_code', ResultCode::SUCCESS);
    }
}
