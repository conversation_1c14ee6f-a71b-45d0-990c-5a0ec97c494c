## Technology Require
```
Laravel Framework: ^12
PHP: ^8.2
Node: ^22
```

## First init
```
cp .env.example .env

composer install

php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link

php artisan ide-helper:generate
```

## Ziggy
```
# Generate ziggy when change router
php artisan ziggy:generate "resources/js/ssr-route/ziggy.js" --types
```

## Laravel passport
```
# generate passpost key
php artisan passport:client --personal

# add header to request api
Content-Type: application/json
X-Requested-With: XMLHttpRequest
Authorization: Bearer ACCESS_TOKEN_KEY
```

## L5 Swagger
```
# generate swagger
php artisan l5-swagger:generate
```

## npm
```
# use vite
npm run dev
```

## Socket server
```
# run socket server
cd socket-server
npm install
npm run socket

# run socket server with pm2
pm2 start ecosystem.config.js
``` 

## URL login
| Name  | Value                        |
|:------|:-----------------------------|
| ADMIN | http://localhost/admin/login |
| API   | http://localhost/api/login   |
