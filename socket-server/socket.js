require('dotenv').config({ path: '../.env' });

const { createServer } = require('http');
const { Server } = require("socket.io");
const Redis = require('ioredis');

const redis = new Redis();

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: "*"
  }
});

// Store connected users
const connectedUsers = new Map();

io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // Handle user authentication
  socket.on('authenticate', (userData) => {
    // Store user data with socket ID
    if (userData.role !== 'admin') {
      connectedUsers.set(socket.id, {
        socketId: socket.id,
        userId: parseInt(userData.userId),
        role: userData.role,
        connectedAt: new Date()
      });
    }

    // Broadcast updated online users count to all clients
    io.emit('online-users-count', getOnlineUsersCount());

    console.log(`User authenticated: ${userData.userId} - ${userData.role} (${socket.id})`);
  });

  socket.on('disconnect', () => {
    // Remove user from connected users
    if (connectedUsers.has(socket.id)) {
      connectedUsers.delete(socket.id);

      // Broadcast updated online users count to all clients
      io.emit('online-users-count', getOnlineUsersCount());
    }
    console.log('Client disconnected:', socket.id);
  });

  // Admin requests for online users list
  socket.on('get-online-users', (data) => {
    // Check if requester is admin (you may want to add more robust authentication)
    if (data && data.role === 'admin') {
      socket.emit('online-users-list', Array.from(connectedUsers.values()));
    }
  });
});

redis.psubscribe("*", (err, count) => {
  if (err) {
    console.error('Redis subscription error:', err);
  }
});

redis.on("pmessage", (pattern, channel, message) => {
  const parsedMessage = JSON.parse(message);
  const event = parsedMessage.event;
  const data = parsedMessage.data;

  if (data && data.userIds && Array.isArray(data.userIds)) {
    sendToSpecificUsers(data.userIds, channel, data);
  } else {
    io.emit(channel, data);
  }
});

function getOnlineUsersCount() {
  return {
    total: connectedUsers.size,
    timestamp: new Date()
  };
}

// Function to send event to specific users
function sendToSpecificUsers(userIds, event, data) {
  for (const [socketId, userData] of connectedUsers.entries()) {
    if (userIds.includes(userData.userId)) {
      const socket = io.sockets.sockets.get(socketId);
      if (socket) {
        socket.emit(event, data);
      }
    }
  }
}



const port = process.env.SOCKET_SERVER_PORT || 6001;
httpServer.listen(port, () => {
  console.log(`Socket.IO server running on port ${port}`);
});
