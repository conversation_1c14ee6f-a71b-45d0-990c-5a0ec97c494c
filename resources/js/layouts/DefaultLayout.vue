<script setup lang="ts">
import SideMenu from '@/components/common/SideMenu.vue';
import HeaderMenu from "@/components/common/HeaderMenu.vue";
import { useSidebar } from "@/composables/useSidebar";

const { isExpanded, isHovered } = useSidebar();

defineProps<{
  author: {
    name: string;
    email: string;
  } | null;
}>();
</script>

<template>
  <div class="min-h-screen xl:flex">
    <side-menu />
    <div
      class="flex-1 transition-all duration-300 ease-in-out"
      :class="[isExpanded || isHovered ? 'lg:ml-[250px]' : 'lg:ml-[90px]']"
    >
      <header-menu :author="author" />
      <div class="p-4 mx-auto max-w-(--breakpoint-2xl) md:p-6">
        <slot/>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
