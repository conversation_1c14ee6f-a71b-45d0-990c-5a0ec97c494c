import { usePage } from '@inertiajs/vue3';

export function useI18n() {
  const page = usePage();

  const t = (key: string, replace: Record<string, string> = {}) => {
    const keys = key.split('.');
    let translation = page.props.translations as Record<string, any>;

    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k];
      } else {
        console.warn(`Translation key "${key}" not found`);
        return key;
      }
    }

    if (typeof translation !== 'string') {
      console.warn(`Translation for "${key}" is not a string`);
      return key;
    }

    return (translation as string).replace(/:(\w+)/g, (match: string, key: string) => {
      return replace[key] || match;
    });
  };

  return {
    t
  };
}
