import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { router } from '@inertiajs/vue3';
import type { InertiaForm } from '@inertiajs/vue3';

export interface UnsavedChangesOptions {
  confirmMessage?: string;
  excludeFields?: string[];
}

export function useUnsavedChanges<T extends Record<string, any>>(
  form: InertiaForm<T>,
  options: UnsavedChangesOptions = {},
) {
  const isDirty = ref(false);
  const showConfirmModal = ref(false);
  const pendingNavigation = ref<(() => void) | null>(null);
  const initialFormData = ref<T | null>(null);
  const isSubmitting = ref(false);

  const {
    confirmMessage = 'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này không?',
    excludeFields = []
  } = options;

  const saveInitialState = () => {
    initialFormData.value = JSON.parse(JSON.stringify(form.data()));
    isDirty.value = false;
  };

  const checkForChanges = () => {
    if (!initialFormData.value) return false;

    const currentData = form.data();
    const initialData = initialFormData.value;

    for (const key in currentData) {
      if (excludeFields.includes(key)) continue;
      const currentValue: any = currentData[key];
      const initialValue = initialData[key];

      if (currentValue instanceof File || initialValue instanceof File) {
        if (currentValue !== initialValue) {
          return true;
        }
      } else if (Array.isArray(currentValue) && Array.isArray(initialValue)) {
        if (JSON.stringify(currentValue) !== JSON.stringify(initialValue)) {
          return true;
        }
      } else if (currentValue !== initialValue) {
        return true;
      }
    }

    return false;
  };

  const updateDirtyState = () => {
    isDirty.value = checkForChanges();
  };

  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    if (isDirty.value && !isSubmitting.value) {
      event.preventDefault();
      event.returnValue = confirmMessage;
      return confirmMessage;
    }
  };

  const confirmLeave = () => {
    showConfirmModal.value = false;
    isDirty.value = false;
    if (pendingNavigation.value) {
      pendingNavigation.value();
      pendingNavigation.value = null;
    }
  };

  const cancelLeave = () => {
    showConfirmModal.value = false;
    pendingNavigation.value = null;
  };

  const markAsClean = () => {
    isDirty.value = false;
    isSubmitting.value = false;
    saveInitialState();
  };

  const markAsDirty = () => {
    isDirty.value = true;
  };

  const markAsSubmitting = () => {
    isSubmitting.value = true;
  };

  const resetSubmitting = () => {
    isSubmitting.value = false;
  };

  let removeNavigationGuard: (() => void) | null = null;

  onMounted(() => {
    nextTick(() => {
      saveInitialState();
    });

    window.addEventListener('beforeunload', handleBeforeUnload);

    removeNavigationGuard = router.on('before', event => {
      if (isDirty.value && !isSubmitting.value) {
        event.preventDefault();
        showConfirmModal.value = true;
        pendingNavigation.value = () => {
          router.visit(event.detail.visit.url);
        };
      }
    });
  });

  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
    if (removeNavigationGuard) {
      removeNavigationGuard();
    }
  });

  return {
    isDirty,
    showConfirmModal,
    updateDirtyState,
    markAsClean,
    markAsDirty,
    markAsSubmitting,
    resetSubmitting,
    confirmLeave,
    cancelLeave,
    saveInitialState,
  };
}
