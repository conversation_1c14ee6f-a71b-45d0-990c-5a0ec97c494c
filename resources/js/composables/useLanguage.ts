import { ref, onMounted } from 'vue';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { route } from 'ziggy-js';
import axios from 'axios';

export const languages = [
  { code: 'en', name: 'English', icon: 'english' },
  { code: 'vi', name: 'Tiếng Việt', icon: 'vietnam' },
  { code: 'jp', name: '日本語', icon: 'japan' }, // Japan
  { code: 'id', name: 'Bahasa Indonesia', icon: 'indonesia' }, // Indonesia
  { code: 'mm', name: 'မြန်မာဘာသာ', icon: 'myanmar' }, // Myanmar
  { code: 'ne', name: 'नेपाली', icon: 'nepal' } // Nepal
];

export function useLanguage() {
  const showLanguageDropdown = ref(false);
  const currentLanguage = ref('');
  const currentLanguageIcon = ref('');
  const isLoading = ref(true);

  onMounted(async () => {
    try {
      const response = await axios.get(route('admin.language.current'));
      const locale = response.data.locale;
      const lang = languages.find(l => l.code === locale);
      if (lang) {
        currentLanguage.value = lang.code.toUpperCase();
        currentLanguageIcon.value = lang.icon;
      }
    } catch (error) {
      console.error('Error getting current language:', error);
      currentLanguage.value = 'JP';
      currentLanguageIcon.value = 'japan';
    } finally {
      isLoading.value = false;
    }
  });

  async function changeLanguage(langCode: string) {
    try {
      await axios.post(route('admin.language.change'), {
        locale: langCode
      });

      const selectedLang = languages.find(l => l.code === langCode);
      if (selectedLang) {
        currentLanguage.value = selectedLang.code.toUpperCase();
        currentLanguageIcon.value = selectedLang.icon;
      }

      window.location.reload();
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      showLanguageDropdown.value = false;
    }
  }

  return {
    showLanguageDropdown,
    currentLanguage,
    currentLanguageIcon,
    changeLanguage,
    languages,
    isLoading
  };
}
