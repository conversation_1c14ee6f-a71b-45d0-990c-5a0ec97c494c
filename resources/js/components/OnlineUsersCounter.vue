<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { usePage } from '@inertiajs/vue3';
import { io, Socket } from 'socket.io-client';
import { useI18n } from "@/composables/useI18n";
import Button from "@/components/common/shared/Button.vue";

const { t } = useI18n();

const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({ total: 0, timestamp: new Date().toISOString() })
  }
});

const emit = defineEmits(['update:onlineUsers']);

const page = usePage();
const socket = ref<Socket | null>(null);
const onlineUsers = ref(props.initialData);
const onlineUsersList = ref<string[]>([]);
const isLoadingUsers = ref(false);

// Connect to socket server
onMounted(() => {
  connectSocket();
});

onUnmounted(() => {
  disconnectSocket();
});

const connectSocket = () => {
  try {
    const socketPort = import.meta.env.VITE_SOCKET_SERVER_PORT || '6001';
    const appUrl = import.meta.env.VITE_APP_URL || 'http://localhost';

    socket.value = io(`${appUrl}:${socketPort}`);

    // Listen for online users count updates
    socket.value.on('online-users-count', (data) => {
      onlineUsers.value = data;
      emit('update:onlineUsers', data);
    });

    socket.value.on('online-users-list', (data) => {
      onlineUsersList.value = data;
      isLoadingUsers.value = false;
    });

    // Authenticate as admin
    const author = page.props.author;
    if (author) {
      socket.value.emit('authenticate', {
        userId: author.id,
        role: 'admin'
      });
    }
  } catch (error) {
    console.error('Socket connection error:', error);
  }
};

const disconnectSocket = () => {
  if (socket.value) {
    socket.value.disconnect();
    socket.value = null;
  }
};

const getOnlineUsersList = () => {
  if (socket.value) {
    isLoadingUsers.value = true;
    socket.value.emit('get-online-users', { role: 'admin' });
  }
};
</script>

<template>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 shadow-sm">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800">{{ t('common.onlineUsers.title') }}</h3>
      <span class="text-sm text-gray-500">
        {{ t('common.onlineUsers.lastUpdated') }}: {{ new Date(onlineUsers.timestamp).toLocaleTimeString() }}
      </span>
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>
        <div>
          <p class="text-sm text-gray-500">{{ t('common.onlineUsers.totalOnline') }}</p>
          <p class="text-2xl font-bold">{{ onlineUsers.total }}</p>
        </div>
      </div>
      <Button
        @click="getOnlineUsersList"
        variant="primary"
        size="sm"
      >
        {{ isLoadingUsers ? t('common.btn.loading') : t('common.btn.refresh') }}
      </Button>
    </div>
  </div>
</template>
