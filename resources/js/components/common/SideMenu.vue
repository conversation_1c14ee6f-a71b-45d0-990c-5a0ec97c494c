<script setup>
import { computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import { usePage } from '@inertiajs/vue3';
import {
  ChartPieIcon,
  UserCircleIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChevronDownIcon,
  BellIcon,
  QuestionMarkCircleIcon,
  ShieldCheckIcon,
  BuildingOfficeIcon,
  BuildingOffice2Icon,
  WrenchScrewdriverIcon,
  EnvelopeIcon,
} from '@heroicons/vue/24/solid';
import { route } from 'ziggy-js';
import { useSidebar } from '@/composables/useSidebar';
import { useI18n } from '@/composables/useI18n';

const page = usePage();
const { isExpanded, isMobileOpen, isHovered, openSubmenu } = useSidebar();
const { t } = useI18n();

const menuItems = [
  {
    name: t('common.sidebar.jobApplication'),
    active: 'job-application',
    icon: BuildingOfficeIcon,
    url: route('admin.job-application.index'),
  },
  {
    name: t('common.sidebar.job'),
    active: 'job',
    icon: BuildingOffice2Icon,
    url: route('admin.job.index'),
  },
  {
    name: t('common.sidebar.user'),
    active: 'user',
    icon: UserGroupIcon,
    url: route('admin.user.index'),
  },
  {
    name: t('common.sidebar.notification'),
    active: 'notification',
    icon: BellIcon,
    url: route('admin.notification.index'),
  },
  {
    name: t('common.sidebar.analytics'),
    active: 'analytics',
    icon: ChartPieIcon,
    url: route('admin.analytic.index'),
  },
  {
    name: t('common.sidebar.faq'),
    active: 'faq',
    icon: QuestionMarkCircleIcon,
    url: route('admin.faq.index'),
  },
  {
    name: t('common.sidebar.setting'),
    icon: Cog6ToothIcon,
    subItems: [
      {
        name: t('common.sidebar.account'),
        active: 'account',
        icon: UserCircleIcon,
        url: route('admin.account.index'),
      },
      {
        name: t('common.sidebar.maintenance'),
        active: 'maintenance',
        icon: WrenchScrewdriverIcon,
        url: route('admin.maintenance.index'),
      },
      {
        name: t('common.sidebar.mailTemplate'),
        active: 'mail-template',
        icon: EnvelopeIcon,
        url: route('admin.mail-template.index'),
      },
      {
        name: t('common.sidebar.policy'),
        active: 'policy',
        icon: ShieldCheckIcon,
        url: route('admin.policy.index'),
      },
    ],
  },
];
const isUrl = (...urls) => {
  const currentUrl = page.url.substr(7);
  if (urls[0] === '') {
    return currentUrl === '';
  }
  return urls.some(url => {
    const urlSegments = url.split('/');
    const currentUrlSegments = currentUrl.split('/');

    return currentUrlSegments[0] === urlSegments[0];
  });
};

const toggleSubmenu = itemIndex => {
  const key = `${itemIndex}`;
  openSubmenu.value = openSubmenu.value === key ? null : key;
};

const isSubmenuOpen = itemIndex => {
  const key = `${itemIndex}`;
  return (
    openSubmenu.value === key ||
    (isAnySubmenuRouteActive.value && menuItems[itemIndex].subItems?.some(subItem => isUrl(subItem.active)))
  );
};

const isAnySubmenuRouteActive = computed(() => {
  return menuItems.some(item => item.subItems && item.subItems.some(subItem => isUrl(subItem.active)));
});
</script>

<template>
  <aside
    :class="[
      'fixed mt-16 flex flex-col lg:mt-0 top-0 px-5 left-0 bg-white text-gray-900 h-screen transition-all duration-300 ease-in-out z-999 border-r border-gray-200',
      {
        'lg:w-[250px]': isExpanded || isMobileOpen || isHovered,
        'lg:w-[90px]': !isExpanded && !isHovered,
        'translate-x-0 w-[290px]': isMobileOpen,
        '-translate-x-full': !isMobileOpen,
        'lg:translate-x-0': true,
      },
    ]"
    @mouseenter="!isExpanded && (isHovered = true)"
    @mouseleave="isHovered = false"
  >
    <div :class="['py-8 flex', !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start']">Logo</div>
    <div class="flex flex-col overflow-y-auto duration-300 ease-linear no-scrollbar">
      <nav class="mb-6">
        <div class="flex flex-col gap-4">
          <div>
            <ul class="flex flex-col gap-2">
              <li v-for="(item, index) in menuItems" :key="index">
                <button
                  v-if="item.subItems"
                  @click="toggleSubmenu(index)"
                  :class="[
                    'menu-item group w-full',
                    {
                      'menu-item-active': isSubmenuOpen(index),
                      'menu-item-inactive': !isSubmenuOpen(index),
                    },
                    !isExpanded && !isHovered ? 'justify-center' : 'justify-start',
                  ]"
                >
                  <span :class="[isSubmenuOpen(index) ? 'menu-item-icon-active' : 'menu-item-icon-inactive']">
                    <component :is="item.icon" class="w-5 h-5"></component>
                  </span>
                  <span v-if="isExpanded || isHovered || isMobileOpen" class="menu-item-text">{{ item.name }}</span>
                  <ChevronDownIcon
                    v-if="isExpanded || isHovered || isMobileOpen"
                    :class="[
                      'ml-auto w-5 h-5 transition-transform duration-200',
                      {
                        'rotate-180 text-brand-500': isSubmenuOpen(index),
                      },
                    ]"
                  />
                </button>
                <Link
                  v-else-if="item.url"
                  :href="item.url"
                  :class="[
                    'menu-item group',
                    {
                      'menu-item-active': isUrl(item.active),
                      'menu-item-inactive': !isUrl(item.active),
                    },
                  ]"
                >
                  <span :class="[isUrl(item.active) ? 'menu-item-icon-active' : 'menu-item-icon-inactive']">
                    <component :is="item.icon" class="w-5 h-5" />
                  </span>
                  <span v-if="isExpanded || isHovered || isMobileOpen" class="menu-item-text">
                    {{ item.name }}
                  </span>
                </Link>
                <transition>
                  <div v-show="isSubmenuOpen(index) && (isExpanded || isHovered || isMobileOpen)">
                    <ul class="mt-2 space-y-2 ml-3">
                      <li v-for="subItem in item.subItems" :key="subItem.name">
                        <Link
                          :href="subItem.url"
                          :class="[
                            'menu-dropdown-item',
                            {
                              'menu-dropdown-item-active': isUrl(subItem.active),
                              'menu-dropdown-item-inactive': !isUrl(subItem.active),
                            },
                          ]"
                        >
                          <component :is="subItem.icon" class="w-5 h-5" />
                          {{ subItem.name }}
                        </Link>
                      </li>
                    </ul>
                  </div>
                </transition>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </aside>
</template>

<style scoped></style>
