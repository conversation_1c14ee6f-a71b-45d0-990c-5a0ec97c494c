<script setup>
import { v4 as uuid } from 'uuid';
import { computed, ref } from 'vue';

const props = defineProps({
  modelValue: [Boolean, String, Number, Array],
  options: {
    type: Object,
    required: true,
  },
  label: String,
  error: String,
  name: String,
  id: {
    type: String,
    default() {
      return `checkbox-${uuid()}`;
    },
  },
});
const searchQuery = ref('');

const emit = defineEmits(['update:modelValue']);

const handleCheckboxChange = (optionValue, isChecked) => {
  let newValue;

  if (Array.isArray(props.modelValue)) {
    newValue = [...props.modelValue];
  } else {
    newValue = [];
  }

  if (isChecked) {
    if (!newValue.includes(optionValue)) {
      newValue.push(optionValue);
    }
  } else {
    const index = newValue.indexOf(optionValue);
    if (index > -1) {
      newValue.splice(index, 1);
    }
  }

  emit('update:modelValue', newValue);
};

const newOptions = computed(() => {
  const options = [];
  if (props.options) {
    if (Array.isArray(props.options)) {
      return props.options;
    } else {
      Object.keys(props.options).forEach(key => {
        options.push({ label: props.options[key], value: key });
      });
    }
  }

  if (props.hasSearch && searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    return options.filter(
      option => option.label.toLowerCase().includes(query) || option.value.toLowerCase().includes(query),
    );
  }

  return options;
});
</script>

<template>
  <div>
    <label v-if="label" class="mb-2 block text-sm font-medium text-gray-700" :for="id">{{ label }}</label>
    <div class="flex items-center">
      <div v-for="option in newOptions" :key="option.value" class="mb-2 mr-4">
        <input
          :id="`${id}-${option.value}`"
          type="checkbox"
          :name="name"
          :value="option.value"
          :checked="Array.isArray(modelValue) && modelValue.includes(option.value)"
          class="mr-2 form-checkbox rounded"
          @change="handleCheckboxChange(option.value, $event.target.checked)"
        />
        <label :for="`${id}-${option.value}`">{{ option.label }}</label>
      </div>
    </div>
    <p v-if="error" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>
