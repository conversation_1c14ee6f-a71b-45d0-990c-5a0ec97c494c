<script setup>
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/solid';
import { router } from '@inertiajs/vue3';
import { useI18n } from "@/composables/useI18n.js";

defineProps({
  paginator: {
    type: {
      currentPage: Number,
      firstPageUrl: String|null,
      from: Number|null,
      lastPage: Number,
      lastPageUrl: String|null,
      nextPageUrl: String|null,
      path: String|null,
      perPage: Number,
      prevPageUrl: String|null,
      to: Number|null,
      total: Number
    },
    required: true
  }
});

const { t } = useI18n();

function navigate(url) {
  if (!url) return;

  const targetUrl = new URL(url, window.location.origin);
  const currentQuery = new URLSearchParams(window.location.search);

  for (const [key, value] of currentQuery.entries()) {
    if (key !== 'page') {
      targetUrl.searchParams.set(key, value);
    }
  }

  router.get(targetUrl.pathname + targetUrl.search, {}, {
    preserveState: true,
    preserveScroll: true,
    replace: true
  });
}
</script>

<template>
  <div class="flex items-center justify-between py-3">
    <!-- Mobile pagination -->
    <div class="flex flex-1 justify-between sm:hidden">
      <button
        @click="navigate(paginator.prevPageUrl)"
        :class="[
          'relative inline-flex items-center rounded-md px-4 py-2 text-sm font-medium',
          paginator.prevPageUrl
            ? 'bg-white text-gray-700 hover:bg-gray-50'
            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
        ]"
        :disabled="!paginator.prevPageUrl"
      >
        Previous
      </button>
      <button
        @click="navigate(paginator.nextPageUrl)"
        :class="[
          'relative ml-3 inline-flex items-center rounded-md px-4 py-2 text-sm font-medium',
          paginator.nextPageUrl
            ? 'bg-white text-gray-700 hover:bg-gray-50'
            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
        ]"
        :disabled="!paginator.nextPageUrl"
      >
        Next
      </button>
    </div>

    <!-- Desktop pagination -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          {{ t('common.pagination', { from: paginator.from, to: paginator.to, total: paginator.total }) }}
        </p>
      </div>

      <div v-if="paginator.lastPage > 1">
        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm">
          <button
            @click="navigate(paginator.prevPageUrl)"
            :class="[
              'relative inline-flex items-center rounded-l-md px-2 py-2',
              paginator.prevPageUrl
                ? 'bg-white text-gray-500 hover:bg-gray-50'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            ]"
            :disabled="!paginator.prevPageUrl"
          >
            <span class="sr-only">Previous</span>
            <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
          </button>

          <template v-for="page in paginator.lastPage" :key="page">
            <button
              @click="navigate(`${paginator.path}?page=${page}`)"
              :class="[
                'relative inline-flex items-center px-4 py-2 text-sm font-medium',
                page === paginator.currentPage
                  ? 'z-10 bg-indigo-600 text-white'
                  : 'bg-white text-gray-500 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
          </template>

          <button
            @click="navigate(paginator.nextPageUrl)"
            :class="[
              'relative inline-flex items-center rounded-r-md px-2 py-2',
              paginator.nextPageUrl
                ? 'bg-white text-gray-500 hover:bg-gray-50'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            ]"
            :disabled="!paginator.nextPageUrl"
          >
            <span class="sr-only">Next</span>
            <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>
