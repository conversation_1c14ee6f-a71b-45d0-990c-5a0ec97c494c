<script setup lang="ts">
import { v4 as uuid } from 'uuid';
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';

interface Option {
  label: string;
  value: string;
}

const props = defineProps({
  id: {
    type: String,
    default() {
      return `select-${uuid()}`;
    },
  },
  options: {
    type: [Object, Array],
    required: true,
  },
  error: String,
  label: String,
  modelValue: [String, Number, Boolean, Array],
  disabled: {
    type: Boolean,
    default: false,
  },
  hasSearch: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);
const selected = ref(props.modelValue);
const searchQuery = ref('');
const isOpen = ref(false);
const dropdownRef = ref<HTMLElement | null>(null);

const newOptions = computed(() => {
  const options: Option[] = [];
  if (props.options) {
    if (Array.isArray(props.options)) {
      return props.options;
    } else {
      Object.keys(props.options).forEach(key => {
        options.push({ label: props.options[key], value: key });
      });
    }
  }

  if (props.hasSearch && searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    return options.filter(
      option => option.label.toLowerCase().includes(query) || option.value.toLowerCase().includes(query),
    );
  }

  return options;
});

const selectedOptions = computed(() => {
  if (props.multiple) {
    const selectedArray = Array.isArray(selected.value) ? selected.value : [];
    return selectedArray.map(value => {
      const option = newOptions.value.find(opt => opt.value == value);
      return option || { label: value, value };
    });
  } else {
    const option = newOptions.value.find(opt => opt.value == selected.value);
    return option ? [option] : [];
  }
});

const selectedLabel = computed(() => {
  if (props.multiple) {
    return '';
  } else {
    const option = newOptions.value.find(opt => opt.value == selected.value);
    return option ? option.label : '';
  }
});

const handleOptionSelect = (optionValue: string) => {
  if (props.multiple) {
    let newValue: string[];

    if (Array.isArray(selected.value)) {
      newValue = [...selected.value];
    } else {
      newValue = [];
    }

    const index = newValue.indexOf(optionValue);
    if (index > -1) {
      newValue.splice(index, 1);
    } else {
      newValue.push(optionValue);
    }

    selected.value = newValue;
    emit('update:modelValue', newValue);
  } else {
    selected.value = optionValue;
    emit('update:modelValue', optionValue);
    isOpen.value = false;
  }
};

const isOptionSelected = (optionValue: string) => {
  if (props.multiple) {
    return Array.isArray(selected.value) && selected.value.includes(optionValue);
  } else {
    return selected.value === optionValue;
  }
};

const removeSelectedOption = (optionValue: string) => {
  if (props.multiple && Array.isArray(selected.value)) {
    const newValue = selected.value.filter(value => value !== optionValue);
    selected.value = newValue;
    emit('update:modelValue', newValue);
  }
};

watch(selected, newSelected => {
  emit('update:modelValue', newSelected);
});

const handleSearch = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
};

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value && props.hasSearch) {
    setTimeout(() => {
      const searchInput = dropdownRef.value?.querySelector('input');
      if (searchInput) {
        searchInput.focus();
      }
    }, 0);
  }
};

const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="relative bg-transparent" ref="dropdownRef">
      <div
        @click="toggleDropdown"
        class="w-full appearance-none rounded-lg border bg-none px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden flex items-center justify-between"
        :class="[
          error ? 'error-input' : 'normal-input',
          disabled ? 'bg-gray-100 cursor-default' : 'bg-transparent cursor-pointer',
          multiple && selectedOptions.length > 0 ? 'min-h-[42px]' : '',
        ]"
      >
        <div class="flex-1">
          <!-- Single select display -->
          <span v-if="!multiple">{{ selectedLabel || '' }}</span>

          <!-- Multiple select badges -->
          <div v-else-if="selectedOptions.length > 0" class="flex flex-wrap gap-1">
            <span
              v-for="option in selectedOptions"
              :key="option.value"
              class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ option.label }}
              <button
                @click.stop="removeSelectedOption(option.value)"
                class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 focus:outline-none"
                type="button"
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </span>
          </div>

          <!-- Placeholder for multiple select when empty -->
          <span v-else-if="multiple" class="text-gray-400">Select options...</span>
        </div>

        <span class="text-gray-700 ml-2">
          <svg
            class="stroke-current"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
              stroke=""
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </span>
      </div>
      <p v-if="error" class="mt-1 text-theme-sm text-error-500">{{ error }}</p>

      <div v-if="isOpen && !disabled" class="absolute z-50 w-full mt-1 bg-white rounded-lg shadow-lg border">
        <div v-if="hasSearch" class="p-2 border-b">
          <input
            type="text"
            class="w-full rounded-lg border bg-transparent px-4 py-2 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden"
            placeholder="Search..."
            @input="handleSearch"
          />
        </div>
        <div class="max-h-60 overflow-y-auto">
          <div
            v-for="option in newOptions"
            :key="option.value"
            class="px-4 py-2 text-sm text-gray-800 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
            :class="{ 'bg-gray-100': isOptionSelected(option.value) }"
            @click="handleOptionSelect(option.value)"
          >
            <span>{{ option.label }}</span>
            <span v-if="multiple && isOptionSelected(option.value)" class="text-blue-600">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path
                  fill-rule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clip-rule="evenodd"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.max-h-60 {
  max-height: 15rem;
}
</style>
