<script setup>
import { v4 as uuid } from 'uuid';
defineProps({
  id: {
    type: String,
    default() {
      return `textarea-${uuid}`;
    },
  },
  error: String,
  label: String,
  modelValue: String,
  disabled: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
});
defineEmits(['update:modelValue']);
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <textarea
      :id="id"
      ref="input"
      v-bind="{ ...$attrs, class: null }"
      class="w-full mb-0 rounded-lg border px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden focus:ring-3"
      :class="[error ? 'error-input' : 'normal-input', disabled ? 'bg-gray-100' : 'bg-transparent']"
      rows="6"
      :value="modelValue"
      :disabled="disabled"
      @input="$emit('update:modelValue', $event.target.value)"
    />
    <div v-if="error" class="mt-1 text-theme-sm text-error-500">{{ error }}</div>
  </div>
</template>

<style scoped></style>
