<script setup>
defineProps({
  loading: {
    type: Boolean,
    default: false
  }
});
</script>

<template>
  <button :disabled="loading" class="flex items-center justify-center">
    <span v-if="loading" class="btn-spinner" />
    <slot v-else />
  </button>
</template>

<style scoped>
.btn-spinner {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  border: 0.2em solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
