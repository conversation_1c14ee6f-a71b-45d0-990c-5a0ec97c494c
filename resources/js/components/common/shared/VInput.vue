<script setup lang="ts">
import { v4 as uuid } from 'uuid';
defineProps({
  modelValue: [String, Number],
  label: String,
  type: {
    type: String,
    default: 'text',
  },
  placeholder: String,
  error: String,
  name: String,
  id: {
    type: String,
    default() {
      return `text-input-${uuid()}`;
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
});

defineEmits(['update:modelValue']);
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="relative mb-1">
      <input
        :id="id"
        ref="input"
        v-bind="{ ...$attrs, class: null }"
        class="w-full rounded-lg border px-4 py-2 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden focus:ring-3"
        :class="[
          error ? 'error-input' : 'normal-input',
          disabled ? 'bg-gray-100' : 'bg-transparent',
        ]"
        :type="type"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
        :placeholder="placeholder"
        :disabled="disabled"
      />
    </div>
    <p v-if="error" class="text-theme-sm text-error-500">{{ error }}</p>
  </div>
</template>
<style scoped></style>
