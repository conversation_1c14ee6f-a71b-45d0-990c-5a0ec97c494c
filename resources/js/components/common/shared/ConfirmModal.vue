<script setup lang="ts">
import { ref, watch } from 'vue';
import { XMarkIcon } from '@heroicons/vue/24/solid';

const props = defineProps({
  id: {
    type: String,
    default: 'modal',
  },
  title: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: 'md',
    validator: (value: string) => ['sm', 'md', 'lg', 'xl', '2xl', '3xl'].includes(value),
  },
  saveButtonText: {
    type: String,
    default: 'Lưu',
  },
  cancelButtonText: {
    type: String,
    default: 'Hủy',
  },
  saveButtonVariant: {
    type: String,
    default: 'primary',
    validator: (value: string) => ['primary', 'secondary', 'danger', 'outline'].includes(value),
  },
  cancelButtonVariant: {
    type: String,
    default: 'outline',
    validator: (value: string) => ['primary', 'secondary', 'danger', 'outline'].includes(value),
  },
  showSaveButton: {
    type: Boolean,
    default: true,
  },
  showCancelButton: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
});

// Define emits
const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'close']);

const isVisible = ref(props.modelValue);

watch(
  () => props.modelValue,
  newValue => {
    isVisible.value = newValue;
  },
);

watch(isVisible, newValue => {
  emit('update:modelValue', newValue);
});

const sizeClasses: Record<string, string> = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-1/3',
  '2xl': 'max-w-1/2',
  '3xl': 'max-w-3/4',
};
const showModal = () => {
  isVisible.value = true;
};

const hideModal = () => {
  isVisible.value = false;
};

const handleSave = () => {
  emit('save');
  if (!props.modelValue) {
    hideModal();
  }
};

const handleCancel = () => {
  emit('cancel');
  hideModal();
};

const handleClose = () => {
  emit('close');
  hideModal();
};

defineExpose({
  showModal,
  hideModal,
});
</script>

<template>
  <div
    :id="id"
    tabindex="-1"
    :class="[
      'overflow-hidden fixed top-0 right-0 left-0 z-999 flex justify-center items-center w-full md:inset-0',
      isVisible ? 'flex' : 'hidden',
    ]"
  >
    <div class="fixed inset-0 bg-gray-400/50 transition-opacity" @click="handleClose"></div>
    <div class="relative p-4 w-full" :class="[sizeClasses[size]]">
      <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
        <button
          type="button"
          class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
          @click="handleClose"
        >
          <x-mark-icon class="w-5 h-5" />
          <span class="sr-only">Đóng</span>
        </button>
        <div class="p-4 md:p-5">
          <h3 v-if="title" class="mb-2 font-semibold text-gray-900 dark:text-white">
            {{ title }}
          </h3>

          <div class="space-y-4 max-h-[75vh] overflow-y-auto">
            <slot></slot>
          </div>

          <div v-if="showSaveButton || showCancelButton" class="flex justify-end mt-3 space-x-2">
            <button
              v-if="showSaveButton"
              type="button"
              :class="[
                'py-2 px-4 text-sm font-medium rounded-lg',
                saveButtonVariant === 'primary'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : saveButtonVariant === 'secondary'
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : saveButtonVariant === 'danger'
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-white border border-gray-300 hover:bg-gray-100 text-gray-700',
              ]"
              @click="handleSave"
            >
              {{ saveButtonText }}
            </button>
            <button
              v-if="showCancelButton"
              type="button"
              :class="[
                'py-2 px-4 text-sm font-medium rounded-lg',
                cancelButtonVariant === 'primary'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : cancelButtonVariant === 'secondary'
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : cancelButtonVariant === 'danger'
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-white border border-gray-300 hover:bg-gray-100 text-gray-700',
              ]"
              @click="handleCancel"
            >
              {{ cancelButtonText }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
