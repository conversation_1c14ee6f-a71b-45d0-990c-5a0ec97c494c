<script setup lang="ts">
import { ref } from 'vue';
import { useToast } from 'vue-toastification';

const toast = useToast();

const props = withDefaults(
  defineProps<{
    modelValue?: File | File[] | null;
    label?: string;
    error?: string;
    accept: string;
    multiple?: boolean;
    required?: boolean;
  }>(),
  {
    required: false,
    multiple: false,
  },
);

const emit = defineEmits<{
  (e: 'update:modelValue', value: File | File[] | null): void;
}>();

const fileInput = ref<HTMLInputElement | null>(null);
const files = ref<
  Array<{
    file: File;
    name: string;
    size: string;
    preview: string | null;
  }>
>([]);
const isDragging = ref(false);

const validateFileType = (file: File): boolean => {
  const acceptedTypes = props.accept.split(',').map(type => type.trim());
  return acceptedTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase());
    }
    return file.type.match(type.replace('*', '.*'));
  });
};

const handleFiles = (newFiles: File[]) => {
  if (!props.multiple && newFiles.length > 1) {
    toast.error('Cannot upload multiple files');
    return;
  }
  const invalidFiles = newFiles.filter(file => !validateFileType(file));

  if (invalidFiles.length > 0) {
    const fileNames = invalidFiles.map(f => f.name).join(', ');
    toast.error(`Files not accept type: ${fileNames}`);
    return;
  }

  const processedFiles = newFiles.map(file => {
    const fileData = {
      file,
      name: file.name,
      size: (file.size / 1024).toFixed(3) + ' KB',
      preview: null as string | null,
    };

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = e => {
        const index = files.value.findIndex(f => f.name === file.name);
        if (index !== -1) {
          files.value[index].preview = e.target?.result as string;
        }
      };
      reader.readAsDataURL(file);
    }

    return fileData;
  });

  files.value = [...files.value, ...processedFiles];
  emit(
    'update:modelValue',
    props.multiple ? files.value.map((f: { file: File }) => f.file) : files.value[0]?.file || null,
  );
};

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const newFiles = Array.from(input.files || []);
  if (newFiles.length) {
    handleFiles(newFiles);
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;

  if (!props.multiple) {
    handleRemoveAll();
  }
  const newFiles = Array.from(event.dataTransfer?.files || []);
  if (newFiles.length) {
    handleFiles(newFiles);
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  isDragging.value = false;
};

const handleChoose = () => fileInput.value?.click();

const handleRemoveFile = (index: number) => {
  files.value.splice(index, 1);
  emit(
    'update:modelValue',
    props.multiple ? files.value.map((f: { file: File }) => f.file) : files.value[0]?.file || null,
  );
};

const handleRemoveAll = () => {
  if (fileInput.value) fileInput.value.value = '';
  files.value = [];
  emit('update:modelValue', null);
};
</script>

<template>
  <div class="" :class="$attrs.class">
    <label v-if="label" class="block text-sm font-medium text-gray-700 mb-1">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div
      class="border-2 border-dashed rounded-lg p-4 transition-colors"
      :class="{
        'border-gray-200 hover:border-gray-300': !isDragging,
        'border-blue-500 bg-blue-50': isDragging,
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
    >
      <div v-if="!files.length" class="flex flex-col items-center justify-center py-4">
        <div class="flex gap-2 mb-2">
          <button
            type="button"
            class="px-3 py-1 bg-gray-900 text-white rounded-lg text-sm font-medium"
            @click="handleChoose"
          >
            + Choose File
          </button>
        </div>
        <p class="text-sm text-gray-500">Drag and drop files to here to upload.</p>
        <p class="text-xs text-gray-400 mt-1">Accepted formats: {{ accept }}</p>
      </div>

      <div v-else class="space-y-3">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium">{{ files.length }} file(s) selected</span>
          <button type="button" class="text-sm text-red-500 hover:text-red-700" @click="handleRemoveAll">
            Remove all
          </button>
        </div>

        <div v-for="(file, index) in files" :key="file.name" class="flex items-start gap-3 p-2 bg-gray-50 rounded">
          <div v-if="file.preview" class="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
            <img :src="file.preview" class="max-w-full max-h-full object-contain" :alt="file.name" />
          </div>
          <div v-else class="w-12 h-12 flex items-center justify-center bg-gray-100 rounded">
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">{{ file.name }}</span>
              <button type="button" class="text-red-500 text-xl" @click="() => handleRemoveFile(index)">×</button>
            </div>
            <div class="text-sm text-gray-500">{{ file.size }}</div>
          </div>
        </div>
      </div>
    </div>

    <input
      ref="fileInput"
      type="file"
      class="hidden"
      @change="handleFileChange"
      :accept="accept"
      :multiple="multiple"
    />

    <div v-if="error" class="mt-1 text-sm text-error-500">{{ error }}</div>
  </div>
</template>
