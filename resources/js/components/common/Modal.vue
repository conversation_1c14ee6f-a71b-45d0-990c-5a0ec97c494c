<script setup lang="ts">
import { XMarkIcon } from '@heroicons/vue/24/solid';
import { onMounted, onUnmounted } from 'vue';

defineProps({
  title: String,
  width: {
    type: String,
    default: 'w-full',
  },
});

onMounted(() => {
  document.body.classList.add('modal-open');
});

onUnmounted(() => {
  document.body.classList.remove('modal-open');
});
</script>

<template>
  <div class="fixed inset-0 flex items-center justify-center modal z-999">
    <div
      class="fixed inset-0 h-full w-full bg-gray-400/50 transition-opacity"
      aria-hidden="true"
      @click="$emit('close')"
    ></div>
    <div
      :class="[
        'no-scrollbar relative max-w-[700px] overflow-y-auto rounded-3xl bg-white p-6',
        width,
      ]"
    >
      <div class="flex items-center justify-between mb-5">
        <h4 class="text-xl font-semibold text-gray-800">
          {{ title }}
        </h4>
        <button
          @click="$emit('close')"
          class="transition-color flex h-11 w-11 items-center justify-center rounded-full bg-gray-100 text-gray-400 hover:bg-gray-200 hover:text-gray-600"
        >
          <XMarkIcon class="w-5 h-5" />
        </button>
      </div>
      <slot name="body"></slot>
    </div>
  </div>
</template>

<style scoped></style>
