<script setup lang="ts">
import { useForm } from "@inertiajs/vue3";
import type { InertiaForm } from '@inertiajs/vue3';
import { useI18n } from "@/composables/useI18n";
import VInput from "@/components/common/shared/VInput.vue";
import Modal from "@/components/common/Modal.vue";
import { DetailUserProps, UserBaseFormType } from "@/types/user";
import Button from "@/components/common/shared/Button.vue";
import VSelect from "@/components/common/shared/VSelect.vue";
import DatePicker from "@/components/common/shared/DatePicker.vue";
import axios from "axios";
import { useToast } from "vue-toastification";
import { watch } from "vue";

const toast = useToast();

const props = defineProps<{
  isOpen: boolean;
  user: DetailUserProps['user'];
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submit', form: InertiaForm<UserBaseFormType>): void;
}>();

const { t } = useI18n();

const userBaseForm = useForm({
  name: props.user.name,
  name_kana: props.user.nameKana,
  name_kanji: props.user.nameKanji,
  phone_number: props.user.phoneNumber,
  gender: props.user.gender,
  birthday: props.user.birthday,
  nationality: props.user.nationality,
  has_certificate: props.user.hasCertificate,
  japanese_level: props.user.japaneseLevel,
  arrival_date: props.user.arrivalDate,
  zip_code: props.user.zipCode,
  prefecture: props.user.prefecture,
  street_address: props.user.streetAddress,
  town_address: props.user.townAddress,
  train_station_name: props.user.trainStationName,
  emergency_name: props.user.emergencyName,
  emergency_relation: props.user.emergencyRelation,
  emergency_phone_number: props.user.emergencyPhoneNumber
});

const genderOptions = window.genderOptions;
const countriesOptions = window.countriesOptions;
const certificateOptions = window.booleanOptions;
const standardLevels = window.standardCertificateLevels;
const sameLevels = window.sameCertificateLevels;

const prefectureWithZipcode = async (zipcode: string) => {
  const response = await axios.get('/api/prefecture-with-zipcode', {
    params: {
      zip_code: zipcode
    }
  });

  if (response.data.result_code === 200) {
    userBaseForm.prefecture = response.data.result_detail.prefectures.prefecture;
    userBaseForm.street_address = response.data.result_detail.prefectures.city + ' ' + response.data.result_detail.prefectures.town_area;
  } else {
    toast.error(response.data.result_error.message);
  }
};

watch(() => userBaseForm.zip_code, (newValue) => {
  if (newValue && newValue.length === 7) {
    prefectureWithZipcode(newValue);
  }
});

const handleSubmit = () => {
  emit('submit', userBaseForm);
};
</script>

<template>
  <Modal v-if="isOpen" @close="emit('close')" title="Edit User Base">
    <template #body>
      <form class="flex flex-col" @submit.prevent="handleSubmit">
        <div class="overflow-y-auto custom-scrollbar max-h-[65vh]">
          <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
            <v-input
              v-model="userBaseForm.name"
              :error="userBaseForm.errors.name"
              class="w-full"
              :label="t('models/user.field.name')"
            />
            <v-input
              v-model="userBaseForm.name_kana"
              :error="userBaseForm.errors.name_kana"
              class="w-full"
              :label="t('models/user.field.nameKana')"
            />
            <v-input
              v-model="userBaseForm.name_kanji"
              :error="userBaseForm.errors.name_kanji"
              class="w-full"
              :label="t('models/user.field.nameKanji')"
            />
            <v-input
              v-model="userBaseForm.phone_number"
              :error="userBaseForm.errors.phone_number"
              class="w-full"
              :label="t('models/user.field.phoneNumber')"
            />
            <v-select
              v-model="userBaseForm.gender"
              :error="userBaseForm.errors.gender"
              :options="genderOptions"
              class="w-full"
              :label="t('models/user.field.gender')"
            />
            <v-input
              v-model="userBaseForm.birthday"
              :error="userBaseForm.errors.birthday"
              class="w-full"
              :label="t('models/user.field.birthday')"
            />
            <v-select
              v-model="userBaseForm.nationality"
              :options="countriesOptions"
              :error="userBaseForm.errors.nationality"
              class="w-full"
              :label="t('models/user.field.nationality')"
              :has-search="true"
            />
            <div class="">
              <label class="mb-1.5 block text-sm font-medium text-gray-700">
                {{ t('models/user.field.japaneseLevel') }}
                <span class="text-red-500">*</span>
              </label>
              <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 ">
                <v-select
                  v-model="userBaseForm.has_certificate"
                  :options="certificateOptions"
                  :error="userBaseForm.errors.has_certificate"
                  class="w-full"
                />
                <v-select
                  v-model="userBaseForm.japanese_level"
                  :error="userBaseForm.errors.japanese_level"
                  :options="userBaseForm.has_certificate == true ? standardLevels : sameLevels"
                  class="w-full"
                />
              </div>
            </div>
            <date-picker
              v-model="userBaseForm.arrival_date"
              :error="userBaseForm.errors.arrival_date"
              type="date"
              class="w-full"
              :label="t('models/user.field.arrivalDate')"
            />
            <v-input
              v-model="userBaseForm.zip_code"
              :error="userBaseForm.errors.zip_code"
              class="w-full"
              :label="t('models/user.field.zipcode')"
            />
            <v-input
              v-model="userBaseForm.prefecture"
              :error="userBaseForm.errors.prefecture"
              class="w-full"
              :label="t('models/user.field.prefecture')"
            />
            <v-input
              v-model="userBaseForm.street_address"
              :error="userBaseForm.errors.street_address"
              class="w-full"
              :label="t('models/user.field.streetAddress')"
            />
            <v-input
              v-model="userBaseForm.town_address"
              :error="userBaseForm.errors.town_address"
              class="w-full"
              :label="t('models/user.field.townAddress')"
            />
            <v-input
              v-model="userBaseForm.train_station_name"
              :error="userBaseForm.errors.train_station_name"
              class="w-full"
              :label="t('models/user.field.trainStationName')"
            />
            <v-input
              v-model="userBaseForm.emergency_name"
              :error="userBaseForm.errors.emergency_name"
              class="w-full"
              :label="t('models/user.field.emergencyName')"
              :required="false"
            />
            <v-input
              v-model="userBaseForm.emergency_relation"
              :error="userBaseForm.errors.emergency_relation"
              class="w-full"
              :label="t('models/user.field.emergencyRelation')"
              :required="false"
            />
            <v-input
              v-model="userBaseForm.emergency_phone_number"
              :error="userBaseForm.errors.emergency_phone_number"
              class="w-full"
              :label="t('models/user.field.emergencyPhone')"
              :required="false"
            />
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button size="sm" variant="primary" type="submit">{{ t('common.btn.save') }}</Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<style scoped>

</style>
