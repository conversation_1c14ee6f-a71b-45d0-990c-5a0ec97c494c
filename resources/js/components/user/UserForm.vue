<script setup lang="ts">
import VInput from "@/components/common/shared/VInput.vue";
import VSelect from "@/components/common/shared/VSelect.vue";
import ButtonLink from "@/components/common/shared/ButtonLink.vue";
import Button from "@/components/common/shared/Button.vue";
import { route } from "ziggy-js";
import { UserFormType } from "@/types/user.ts";
import type { InertiaForm } from '@inertiajs/vue3';
import DatePicker from "@/components/common/shared/DatePicker.vue";
import axios from "axios";
import { watch } from "vue";
import { useToast } from "vue-toastification";
import { useI18n } from "@/composables/useI18n.ts";
import VFileInput from "@/components/common/shared/VFileInput.vue";

const { t } = useI18n();

interface UserFormProps {
  form: InertiaForm<UserFormType>,
  isEditing: boolean,
  userId: number | null
}
const props = defineProps<UserFormProps>();
const toast = useToast();

const genderOptions = window.genderOptions;
const bankTypeOptions = window.bankTypeOptions;
const countriesOptions = window.countriesOptions;
const periodTypeOptions = window.periodTypeOptions;
const certificateOptions = window.booleanOptions;
const standardLevels = window.standardCertificateLevels;
const sameLevels = window.sameCertificateLevels;

const prefectureWithZipcode = async (zipcode: string) => {
  const response = await axios.get('/api/zipcode-data', {
    params: {
      zip_code: zipcode
    }
  });

  if (response.data.result_code === 200) {
    props.form.prefecture = response.data.result_detail.prefecture.prefecture;
    props.form.street_address = response.data.result_detail.prefecture.city + ' ' + response.data.result_detail.prefecture.townArea;
    if (response.data.result_detail.stations.length > 0) {
      props.form.train_station_name = response.data.result_detail.stations[0].name;
    }
  } else {
    toast.error(response.data.result_error.message);
  }
};

watch(() => props.form.zip_code, (newValue) => {
  if (newValue && newValue.length === 7) {
    prefectureWithZipcode(newValue);
  }
});
</script>

<template>
  <div>
    <form @submit.prevent="$emit('submit')" enctype="multipart/form-data">
      <div class="flex flex-wrap">
        <div class="w-full rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('models/user.cardTitle.baseInfo') }}
          </h4>
          <v-input
            v-model="form.email"
            :error="form.errors.email"
            type="email"
            class="pb-6 w-full"
            :label="t('models/user.field.email')"
          />
          <v-input
            v-model="form.phone_number"
            :error="form.errors.phone_number"
            class="pb-6 w-full"
            :label="t('models/user.field.phoneNumber')"
          />
          <v-file-input
            v-model="form.avatar"
            :error="form.errors.avatar"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/user.field.avatar')"
          />
          <v-file-input
            v-model="form.health_certificate"
            :error="form.errors.health_certificate"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/user.field.healthCertificate')"
          />
          <v-input
            v-model="form.name"
            :error="form.errors.name"
            class="pb-6 w-full"
            :label="t('models/user.field.name')"
          />
          <v-input
            v-model="form.name_kana"
            :error="form.errors.name_kana"
            class="pb-6 w-full"
            :label="t('models/user.field.nameKana')"
          />
          <v-input
            v-model="form.name_kanji"
            :error="form.errors.name_kanji"
            class="pb-6 w-full"
            :label="t('models/user.field.nameKanji')"
          />
          <v-input
            v-if="!isEditing"
            v-model="form.password"
            :error="form.errors.password"
            type="password"
            class="pb-6 w-full"
            :label="t('models/user.field.password')"
          />
          <v-input
            v-if="!isEditing"
            v-model="form.password_confirmation"
            :error="form.errors.password_confirmation"
            type="password"
            class="pb-6 w-full"
            :label="t('models/user.field.confirmPassword')"
          />
          <v-select
            v-model="form.gender"
            :error="form.errors.gender"
            :options="genderOptions"
            class="pb-6 w-full"
            :label="t('models/user.field.gender')"
          />
          <date-picker
            v-model="form.birthday"
            :error="form.errors.birthday"
            type="date"
            class="pb-6 w-full"
            :label="t('models/user.field.birthday')"
          />
          <v-select
            v-model="form.nationality"
            :options="countriesOptions"
            :error="form.errors.nationality"
            class="pb-6 w-full"
            :label="t('models/user.field.nationality')"
            :has-search="true"
          />
          <div class="">
            <label class="mb-1.5 block text-sm font-medium text-gray-700">
              {{ t('models/user.field.japaneseLevel') }}
              <span class="text-red-500">*</span>
            </label>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 ">
              <v-select
                v-model="form.has_certificate"
                :options="certificateOptions"
                :error="form.errors.has_certificate"
                class="pb-6 w-full"
              />
              <v-select
                v-model="form.japanese_level"
                :error="form.errors.japanese_level"
                :options="form.has_certificate == true ? standardLevels : sameLevels"
                class="pb-6 w-full"
              />
            </div>
          </div>
          <date-picker
            v-model="form.arrival_date"
            :error="form.errors.arrival_date"
            type="date"
            class="pb-6 w-full"
            :label="t('models/user.field.arrivalDate')"
          />
        </div>
        <div class="w-full mt-5 rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('models/user.cardTitle.address') }}
          </h4>
          <v-input
            v-model="form.zip_code"
            :error="form.errors.zip_code"
            class="pb-6 w-full"
            :label="t('models/user.field.zipcode')"
          />
          <v-input
            v-model="form.prefecture"
            :error="form.errors.prefecture"
            class="pb-6 w-full"
            :label="t('models/user.field.prefecture')"
          />
          <v-input
            v-model="form.street_address"
            :error="form.errors.street_address"
            class="pb-6 w-full"
            :label="t('models/user.field.streetAddress')"
          />
          <v-input
            v-model="form.town_address"
            :error="form.errors.town_address"
            class="pb-6 w-full"
            :label="t('models/user.field.townAddress')"
          />
          <v-input
            v-model="form.train_station_name"
            :error="form.errors.train_station_name"
            class="pb-6 w-full"
            :label="t('models/user.field.trainStationName')"
          />
        </div>
        <div class="w-full mt-5 rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('models/user.cardTitle.residenceCard') }}
          </h4>
          <v-file-input
            v-model="form.front_card"
            :error="form.errors.front_card"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/residence_card.field.frontCard')"
          />
          <v-file-input
            v-model="form.back_card"
            :error="form.errors.back_card"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/residence_card.field.backCard')"
          />
          <date-picker
            v-model="form.period_of_stay"
            :error="form.errors.period_of_stay"
            class="pb-6 w-full"
            type="date"
            :label="t('models/residence_card.field.periodOfStay')"
          />
          <v-select
            v-model="form.period_type"
            :error="form.errors.period_type"
            :options="periodTypeOptions"
            class="pb-6 w-full"
            :label="t('models/residence_card.field.periodType')"
          />
          <v-file-input
            v-model="form.identification"
            :error="form.errors.identification"
            accept="image/*"
            class="pb-6 w-full"
            :label="
              form.period_type === 'STUDENT' ?
              t('models/residence_card.field.studentCard') :
              t('models/residence_card.field.identification')
            "
          />
          <v-input
            v-if="form.period_type === 'STUDENT'"
            v-model="form.school_name"
            :error="form.errors.school_name"
            class="pb-6 w-full"
            :label="t('models/residence_card.field.schoolName')"
          />
          <date-picker
            v-model="form.period_expire_at"
            :error="form.errors.period_expire_at"
            type="date"
            class="pb-6 w-full"
            :label="t('models/residence_card.field.periodExpireAt')"
          />
          <v-file-input
            v-model="form.passport_image"
            :error="form.errors.passport_image"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/user.field.passportImage')"
          />
          <v-input
            v-model="form.passport_number"
            :error="form.errors.passport_number"
            class="pb-6 w-full"
            :label="t('models/user.field.passportNumber')"
          />
          <date-picker
            v-model="form.passport_expired_at"
            :error="form.errors.passport_expired_at"
            type="date"
            class="pb-6 w-full"
            :label="t('models/user.field.passportExpiredAt')"
          />
        </div>
        <div class="w-full mt-5 rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('models/user.cardTitle.emergencyBank') }}
          </h4>
          <v-input
            v-model="form.emergency_name"
            :error="form.errors.emergency_name"
            class="pb-6 w-full"
            :label="t('models/user.field.emergencyName')"
            :required="false"
          />
          <v-input
            v-model="form.emergency_relation"
            :error="form.errors.emergency_relation"
            class="pb-6 w-full"
            :label="t('models/user.field.emergencyRelation')"
            :required="false"
          />
          <v-input
            v-model="form.emergency_phone_number"
            :error="form.errors.emergency_phone_number"
            class="pb-6 w-full"
            :label="t('models/user.field.emergencyPhone')"
            :required="false"
          />
          <v-select
            v-model="form.bank_type"
            :error="form.errors.bank_type"
            :options="bankTypeOptions"
            class="pb-6 w-full"
            :label="t('models/user.field.bankType')"
          />
          <v-file-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.atm_image"
            :error="form.errors.atm_image"
            accept="image/*"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.atm')"
          />
          <v-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.bank_name"
            :error="form.errors.bank_name"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.bankName')"
          />
          <v-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.bank_branch"
            :error="form.errors.bank_branch"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.bankBranch')"
          />
          <v-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.deposit_type"
            :error="form.errors.deposit_type"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.depositType')"
          />
          <v-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.account_name"
            :error="form.errors.account_name"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.accountName')"
          />
          <v-input
            v-if="form.bank_type === 'BANK'"
            v-model="form.account_number"
            :error="form.errors.account_number"
            class="pb-6 w-full"
            :label="t('models/user_bank.field.accountNumber')"
          />
        </div>
      </div>
      <div class="flex items-center justify-end gap-4 py-4">
        <ButtonLink
          size="sm"
          variant="outline"
          :href="route('admin.user.index')"
        >
          {{ t('common.btn.cancel') }}
        </ButtonLink>
        <Button
          size="sm"
          variant="primary"
          type="submit"
        >
          {{ t('common.btn.save') }}
        </Button>
      </div>
    </form>
  </div>
</template>
