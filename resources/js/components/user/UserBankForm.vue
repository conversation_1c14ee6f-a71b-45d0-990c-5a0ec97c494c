<script setup lang="ts">
import { useForm } from "@inertiajs/vue3";
import { useI18n } from "@/composables/useI18n";
import VInput from "@/components/common/shared/VInput.vue";
import Modal from "@/components/common/Modal.vue";
import { DetailUserProps } from "@/types/user";
import Button from "@/components/common/shared/Button.vue";
import VSelect from "@/components/common/shared/VSelect.vue";

const props = defineProps<{
  isOpen: boolean;
  user: DetailUserProps['user'];
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submit', form: any): void;
}>();

const { t } = useI18n();

const userBankForm = useForm({
  bank_type: props.user.bankType,
  bank_name: props.user.bankName,
  bank_branch: props.user.bankBranch,
  deposit_type: props.user.depositType,
  account_name: props.user.accountName,
  account_number: props.user.accountNumber
});

const bankTypeOptions = window.bankTypeOptions;

const handleSubmit = () => {
  emit('submit', userBankForm);
};
</script>

<template>
  <Modal v-if="isOpen" @close="emit('close')" title="Edit User Bank">
    <template #body>
      <form class="flex flex-col" @submit.prevent="handleSubmit">
        <div class="overflow-y-auto custom-scrollbar max-h-[65vh] min-h-40">
          <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
            <v-select
              v-model="userBankForm.bank_type"
              :error="userBankForm.errors.bank_type"
              :options="bankTypeOptions"
              class="w-full"
              :label="t('models/user.field.bankType')"
            />
            <v-input
              v-if="userBankForm.bank_type === 'BANK'"
              v-model="userBankForm.bank_name"
              :error="userBankForm.errors.bank_name"
              class="w-full"
              :label="t('models/user_bank.field.bankName')"
            />
            <v-input
              v-if="userBankForm.bank_type === 'BANK'"
              v-model="userBankForm.bank_branch"
              :error="userBankForm.errors.bank_branch"
              class="w-full"
              :label="t('models/user_bank.field.bankBranch')"
            />
            <v-input
              v-if="userBankForm.bank_type === 'BANK'"
              v-model="userBankForm.deposit_type"
              :error="userBankForm.errors.deposit_type"
              class="w-full"
              :label="t('models/user_bank.field.depositType')"
            />
            <v-input
              v-if="userBankForm.bank_type === 'BANK'"
              v-model="userBankForm.account_name"
              :error="userBankForm.errors.account_name"
              class="w-full"
              :label="t('models/user_bank.field.accountName')"
            />
            <v-input
              v-if="userBankForm.bank_type === 'BANK'"
              v-model="userBankForm.account_number"
              :error="userBankForm.errors.account_number"
              class="w-full"
              :label="t('models/user_bank.field.accountNumber')"
            />
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button size="sm" variant="primary" type="submit">{{ t('common.btn.save') }}</Button>
        </div>
      </form>
    </template>
  </Modal>
</template>
