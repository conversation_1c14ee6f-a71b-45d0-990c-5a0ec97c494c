<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/solid';
import VPagination from '@/components/common/shared/VPagination.vue';
import VInput from '@/components/common/shared/VInput.vue';
import Button from '@/components/common/shared/Button.vue';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { ListNotificationProps } from '@/types/notification.ts';

defineProps<ListNotificationProps>();

const { t } = useI18n();

const searchForm = useForm({
  type: '',
  title: '',
});

function submitSearch() {
  searchForm.get(route('admin.notification.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.notification.index'));
}

function deleteNotification(notificationId: number) {
  if (confirm(t('common.confirm_delete'))) {
    router.delete(route('admin.notification.delete', notificationId));
  }
}
</script>

<template>
  <Head title="Notification" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white/90">
      {{ t('models/notification.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('common.field.search') }}
          </h4>
          <form @submit.prevent="submitSearch">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
              <div>
                <v-input
                  label="Type"
                  v-model="searchForm.type"
                  placeholder="Enter type..."
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-input
                  label="Title"
                  v-model="searchForm.title"
                  placeholder="Enter title..."
                  class="w-full"
                  :required="false"
                />
              </div>
            </div>
            <div class="flex items-center justify-center gap-2 mt-6">
              <Button size="sm" variant="outline" type="button" @click="resetSearch">{{
                t('common.btn.reset')
              }}</Button>
              <Button size="sm" variant="primary" type="submit">{{
                t('common.btn.search')
              }}</Button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="flex items-center justify-end mb-6">
      <button-link size="sm" variant="primary" :href="route('admin.notification.create')">{{
        t('common.btn.create')
      }}</button-link>
    </div>
    <div class="overflow-hidden rounded-xl border border-gray-200 bg-white">
      <div class="max-w-full overflow-x-auto custom-scrollbar">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th
                scope="col"
                class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                {{ t('models/notification.field.id') }}
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                {{ t('models/notification.field.title') }}
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                {{ t('models/notification.field.start_notification_at') }}
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                {{ t('models/notification.field.end_notification_at') }}
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase"
              >
                {{ t('common.field.action') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr
              v-for="notification in notifications.data"
              :key="notification.id"
              class="border-t border-gray-100"
            >
              <td class="border-t">
                <span class="px-5 py-4 sm:px-6">{{ notification.id }}</span>
              </td>
              <td class="border-t">
                <span class="px-5 py-4 sm:px-6">{{ notification.title }}</span>
              </td>
              <td class="border-t">
                <span class="px-5 py-4 sm:px-6">{{ notification.startNotificationAt }}</span>
              </td>
              <td class="border-t">
                <span class="px-5 py-4 sm:px-6">{{ notification.endNotificationAt }}</span>
              </td>
              <td class="px-5 py-4 sm:px-6">
                <div class="flex text-center">
                  <Link :href="route('admin.notification.show', notification.id)">
                    <eye-icon class="size-5 mr-2" />
                  </Link>
                  <Link :href="route('admin.notification.edit', notification.id)">
                    <pencil-square-icon class="size-5 mr-2" />
                  </Link>
                  <trash-icon
                    class="size-5 mr-2 text-red-600 cursor-pointer"
                    @click="deleteNotification(notification.id)"
                  />
                </div>
              </td>
            </tr>
          </tbody>
          <tr v-if="notifications.data.length === 0">
            <td class="px-6 py-4 border-t" colspan="5">{{ t('common.noResult') }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="mt-2" v-if="notifications.data.length > 0">
      <v-pagination :paginator="notifications.paginator" />
    </div>
  </div>
</template>

<style scoped></style>
