<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n';
import { route } from 'ziggy-js';
import Button from '@/components/common/shared/Button.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import VInput from '@/components/common/shared/VInput.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import VTextarea from '@/components/common/shared/VTextarea.vue';
import NotificationForm from '@/pages/Notification/common/NotificationForm.vue';
import Modal from '@/components/common/Modal.vue';
import { ref } from 'vue';
import { NotificationFormType } from '@/types/notification.ts';

const { t } = useI18n();
const isPreview = ref(false);

const form = useForm<NotificationFormType>({
  type: '',
  title: '',
  body: '',
  job_id: '',
  start_notification_at: '',
  end_notification_at: '',
});

function store() {
  form.post(route('admin.notification.store'));
}
</script>

<template>
  <Head title="Notification" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/notification.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <NotificationForm :form-data="form" @openPreview="isPreview = true" />
  </div>
  <Modal
    v-if="isPreview"
    @close="isPreview = false"
    :title="t('models/notification.screenName.edit')"
  >
    <template #body>
      <NotificationForm
        :form-data="form"
        @submit="store"
        @openPreview="isPreview = true"
        @close="isPreview = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
