<script setup lang="ts">
import { DetailNotificationProps } from '@/types/notification.ts';
import { useI18n } from '@/composables/useI18n.ts';
import { route } from 'ziggy-js';
import { Head } from '@inertiajs/vue3';
import { PencilSquareIcon } from '@heroicons/vue/24/solid';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';

defineProps<DetailNotificationProps>();

const { t } = useI18n();
</script>

<template>
  <Head :title="t('models/notification.title')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/notification.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div>
            <div class="grid grid-cols-1 gap-4 lg:gap-7">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/notification.field.title') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ notification.title }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/notification.field.start_notification_at') }}
                </p>
                <p class="text-sm font-medium text-gray-800">
                  {{ notification.startNotificationAt }}
                </p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/notification.field.end_notification_at') }}
                </p>
                <p class="text-sm font-medium text-gray-800">
                  {{ notification.endNotificationAt }}
                </p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/notification.field.body') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ notification.body }}</p>
              </div>
            </div>
          </div>
          <ButtonLink :href="route('admin.notification.edit', notification.id)" class="edit-button">
            <PencilSquareIcon class="w-5 h-5" />
            {{ t('common.btn.edit') }}
          </ButtonLink>
        </div>
      </div>
      <div class="flex justify-end">
        <ButtonLink size="sm" variant="outline" :href="route('admin.notification.index')">{{
          t('common.btn.back')
        }}</ButtonLink>
      </div>
    </div>
  </div>
</template>
