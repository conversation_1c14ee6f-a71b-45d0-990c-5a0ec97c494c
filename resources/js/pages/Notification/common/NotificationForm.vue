<script setup lang="ts">
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import { useI18n } from '@/composables/useI18n.ts';
import { InertiaForm } from '@inertiajs/vue3';
import { NotificationFormType } from '@/types/notification.ts';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import VTextarea from '@/components/common/shared/VTextarea.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';

const props = defineProps<{
  isEdit?: boolean;
  isPreview?: boolean;
  formData: InertiaForm<NotificationFormType>;
}>();

const emit = defineEmits<{
  (e: 'openPreview'): void;
  (e: 'close'): void;
  (e: 'submit', form: InertiaForm<NotificationFormType>): void;
}>();

const { t } = useI18n();

const form = props.formData;

const handleSubmit = () => {
  emit('submit', form);
};
</script>

<template>
  <form class="flex flex-col" @submit.prevent="handleSubmit">
    <div :class="isPreview ? 'overflow-y-auto custom-scrollbar max-h-[65vh]' : ''">
      <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
        <v-input
          v-model="form.title"
          :error="form.errors.title"
          class="w-full"
          :label="t('models/notification.field.title')"
          :disabled="isPreview"
        />
        <div class="w-full">
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
            <date-picker
              v-model="form.start_notification_at"
              :error="form.errors.start_notification_at"
              type="datetime"
              class="pb-6 w-full"
              :label="t('models/notification.field.startNotificationAt')"
              :disabled="isPreview"
            />
            <date-picker
              v-model="form.end_notification_at"
              :error="form.errors.end_notification_at"
              type="datetime"
              class="pb-6 w-full"
              :label="t('models/notification.field.endNotificationAt')"
              :disabled="isPreview"
            />
          </div>
        </div>
        <v-textarea
          v-model="form.body"
          :error="form.errors.body"
          class="pb-6 w-full"
          :label="t('models/notification.field.body')"
          :disabled="isPreview"
        />
      </div>
    </div>
    <div v-if="isPreview" class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="primary" type="submit">{{
        t('common.btn.save')
      }}</Button>
      <Button size="sm" variant="outline" type="button" @click="emit('close')">{{
        t('common.btn.cancel')
      }}</Button>
    </div>
    <div v-else class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="outline" @click="emit('openPreview')">{{
        t('common.btn.preview')
      }}</Button>
      <ButtonLink :href="route('admin.notification.index')" size="sm" variant="outline">{{
        t('common.btn.cancel')
      }}</ButtonLink>
    </div>
  </form>
</template>

<style scoped></style>
