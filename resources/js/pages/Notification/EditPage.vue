<script setup lang="ts">
import { Head, InertiaForm, useForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import { ref } from 'vue';
import Modal from '@/components/common/Modal.vue';
import { DetailNotificationProps, NotificationFormType } from '@/types/notification.ts';
import NotificationForm from '@/pages/Notification/common/NotificationForm.vue';

const props = defineProps<DetailNotificationProps>();

const { t } = useI18n();
const isPreviewEdit = ref(false);

const formData = useForm<NotificationFormType>({
  type: props.notification.type,
  title: props.notification.title,
  body: props.notification.body,
  job_id: props.notification.jobId,
  start_notification_at: props.notification.startNotificationAt,
  end_notification_at: props.notification.endNotificationAt,
});

const update = (form: InertiaForm<NotificationFormType>) => {
  form.put(route('admin.notification.update', props.notification.id), {
    onSuccess: () => {
      isPreviewEdit.value = false;
    },
  });
};
</script>

<template>
  <Head :title="t('models/notification.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/notification.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <NotificationForm :form-data="formData" @openPreview="isPreviewEdit = true" />
  </div>
  <Modal
    v-if="isPreviewEdit"
    @close="isPreviewEdit = false"
    :title="t('models/notification.screenName.edit')"
  >
    <template #body>
      <NotificationForm
        :form-data="formData"
        @submit="update"
        @openPreview="isPreviewEdit = true"
        @close="isPreviewEdit = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
