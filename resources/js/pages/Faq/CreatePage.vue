<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from "@/composables/useI18n.ts";
import { route } from "ziggy-js";
import VInput from "@/components/common/shared/VInput.vue";
import VTextarea from "@/components/common/shared/VTextarea.vue";
import Button from "@/components/common/shared/Button.vue";
import VCheckbox from "@/components/common/shared/VCheckbox.vue";

const { t } = useI18n();

const form = useForm({
  question: '',
  answer: '',
  is_public: false
});

function submit() {
  form.post(route('admin.faq.store'));
}
</script>

<template>
  <Head :title="t('models/faq.title')"/>
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/faq.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <form @submit.prevent="submit">
      <div class="grid grid-cols-1 gap-6">
        <div>
          <v-input
            v-model="form.question"
            :error="form.errors.question"
            :label="t('models/faq.field.question')"
            required
          />
        </div>
        <div>
          <v-textarea
            v-model="form.answer"
            :error="form.errors.answer"
            :label="t('models/faq.field.answer')"
            required
          />
        </div>
        <div>
          <v-checkbox
            v-model="form.is_public"
            :error="form.errors.is_public"
            :label="t('models/faq.field.isPublic')"
          />
        </div>
        <div class="flex justify-end">
          <Button size="sm" type="submit" variant="primary">{{ t('common.btn.save') }}</Button>
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>

</style>
