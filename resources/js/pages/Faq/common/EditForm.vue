<script setup lang="ts">
import Button from "@/components/common/shared/Button.vue";
import VInput from "@/components/common/shared/VInput.vue";
import { useI18n } from "@/composables/useI18n.ts";
import { InertiaForm } from "@inertiajs/vue3";
import VTextarea from "@/components/common/shared/VTextarea.vue";
import VCheckbox from "@/components/common/shared/VCheckbox.vue";
import { FaqFormType } from "@/types/faq.ts";
import ButtonLink from "@/components/common/shared/ButtonLink.vue";
import { route } from "ziggy-js";

const props = defineProps<{
  isPreview?: boolean,
  formData: InertiaForm<FaqFormType>;
}>();

const emit = defineEmits<{
  (e: 'openPreview'): void;
  (e: 'close'): void;
  (e: 'submit', form: InertiaForm<FaqFormType>): void;
}>();

const { t } = useI18n();
const form = props.formData;

const handleSubmit = () => {
  emit('submit', form);
};
</script>

<template>
  <form class="flex flex-col" @submit.prevent="handleSubmit">
    <div class="overflow-y-auto custom-scrollbar max-h-[65vh]">
      <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
        <v-input
          v-model="form.question"
          :error="form.errors.question"
          class="w-full"
          :label="t('models/faq.field.question')"
          :disabled="isPreview"
        />
        <v-textarea
          v-model="form.answer"
          :error="form.errors.answer"
          class="pb-6 w-full"
          :label="t('models/faq.field.answer')"
          :disabled="isPreview"
        />
        <v-checkbox
          v-model="form.is_public"
          :error="form.errors.is_public"
          class="pb-6 w-full"
          :label="t('models/faq.field.isPublic')"
          :disabled="isPreview"
        />
      </div>
    </div>
    <div v-if="isPreview" class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="primary" type="submit">{{ t('common.btn.save') }}</Button>
      <Button size="sm" variant="outline" type="button" @click="emit('close')">{{ t('common.btn.cancel') }}</Button>
    </div>
    <div v-else class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="outline" @click="emit('openPreview')">{{ t('common.btn.preview') }}</Button>
      <ButtonLink :href="route('admin.faq.index')" size="sm" variant="outline">{{ t('common.btn.cancel') }}</ButtonLink>
    </div>
  </form>
</template>
