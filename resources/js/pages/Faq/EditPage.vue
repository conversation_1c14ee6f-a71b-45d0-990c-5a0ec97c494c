<script setup lang="ts">
import { DetailFaqProps, FaqFormType } from "@/types/faq.ts";
import { useI18n } from "@/composables/useI18n.ts";
import { ref } from "vue";
import { Head, InertiaForm, useForm } from "@inertiajs/vue3";
import { route } from "ziggy-js";
import EditForm from "@/pages/Faq/common/EditForm.vue";
import Modal from "@/components/common/Modal.vue";

const props = defineProps<DetailFaqProps>();

const { t } = useI18n();
const isPreviewEdit = ref(false);

const formData = useForm<FaqFormType>({
  question: props.faq.question,
  answer: props.faq.answer,
  is_public: props.faq.isPublic
});

const update = (form: InertiaForm<FaqFormType>) => {
  form.put(route('admin.faq.update', props.faq.id), {
    onSuccess: () => {
      isPreviewEdit.value = false;
    }
  });
};
</script>

<template>
  <Head :title="t('models/faq.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/faq.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <EditForm
      :form-data="formData"
      @openPreview="isPreviewEdit = true"
    />
  </div>
  <Modal v-if="isPreviewEdit" @close="isPreviewEdit = false" :title="t('models/faq.screenName.edit')">
    <template #body>
      <EditForm
        :form-data="formData"
        @submit="update"
        @openPreview="isPreviewEdit = true"
        @close="isPreviewEdit = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
