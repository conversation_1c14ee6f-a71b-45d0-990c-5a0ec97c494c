<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3';
import { useRoute } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import SearchForm from '@/pages/JobApplication/common/SearchForm.vue';
import JobCalendar from '@/pages/JobApplication/common/JobCalendar.vue';
import { EyeIcon } from '@heroicons/vue/24/solid';
import { ListJobProps } from '@/types/job.ts';
import VPagination from '@/components/common/shared/VPagination.vue';

const props = defineProps<ListJobProps>();

const route = useRoute();
const { t } = useI18n();
</script>

<template>
  <Head :title="t('models/job_application.title')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job_application.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <JobCalendar :jobs="jobs.data" />
    <SearchForm @search="search" @reset="reset" />
    <div class="inline-block min-w-full align-middle">
      <div class="overflow-hidden border rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.id') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.title') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.prefecture') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.job_time') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.quantity') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job.field.is_filled') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('common.field.action') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="job in jobs.data" :key="job.id" class="border-t border-gray-100">
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ job.id }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ job.title }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ job.prefecture }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ `${job.timeStart} - ${job.timeEnd}` }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ `${job.countApplied}/${job.quantity}` }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{
                  job.isFilled ? t('models/job.is_filled.ended') : t('models/job.is_filled.not_ended')
                }}</span>
              </td>
              <td class="px-3 py-4 text-sm">
                <div class="flex justify-center">
                  <Link :href="route('admin.job-application.show', job.id)"><eye-icon class="size-5 mr-2" /></Link>
                </div>
              </td>
            </tr>
            <tr v-if="jobs.data.length === 0">
              <td class="px-6 py-4 border-t" colspan="6">{{ t('common.noResult') }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2" v-if="jobs.data.length > 0">
        <v-pagination :paginator="jobs.paginator" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
