<script setup lang="ts">
import { Head } from "@inertiajs/vue3";
import type { InertiaForm } from '@inertiajs/vue3';
import { route } from "ziggy-js";
import { UserBankFormType, UserBaseFormType, UserType } from "@/types/user.ts";
import UserBaseForm from "@/components/user/UserBaseForm.vue";
import UserBankForm from "@/components/user/UserBankForm.vue";
import { useI18n } from "@/composables/useI18n.ts";


interface Props {
  user: UserType | null;
}

const { user } = defineProps<Props>();

const { t } = useI18n();

const handleSubmitUserBase = (form: InertiaForm<UserBaseFormType>) => {
  form.patch(route('admin.user.update.base', user.id), {
    onSuccess: () => {
      isEditUserBase.value = false;
    }
  });
};

const handleSubmitUserBank = (form: InertiaForm<UserBankFormType>) => {
  form.patch(route('admin.user.update.bank', user.id), {
    onSuccess: () => {
      isEditUserBank.value = false;
    }
  });
};
</script>

<template>
  <Head :title="t('models/user.title')"/>
  <div v-if="user">
  <div class="">
    <div>
      <div class="p-5 mb-6 border rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div class="w-full">
            <div class="flex items-center space-x-4 mb-6">
              <img
                :src="user.avatarUrl || '/images/ava-default.png'"
                alt="Avatar"
                class="w-24 h-24 rounded-full object-cover"
              >
              <div>
                <p class="text-lg font-medium text-gray-800">{{ user.name }}</p>
                <p class="text-sm text-gray-600">{{ user.email }}</p>
              </div>
            </div>
            <div class="grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.code') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.code }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.name_kana') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nameKana }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.name_kanji') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nameKanji }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.health_certificate') }}</p>
                <div class="text-sm font-medium text-gray-800">
                  <img class="w-full max-w-60" :src="user.healthCertificateUrl" alt="" />
                </div>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.phone_number') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.phoneNumber }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.gender') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.genderName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.birthday') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.birthday }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.nationality') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nationalityName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.japanese_level') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.japaneseLevelName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.arrival_date') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.arrivalDate }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.zipcode') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.zipCode }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.prefecture') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.prefecture }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.street_address') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.streetAddress }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.town_address') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.townAddress }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.train_station_name') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.trainStationName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergency_name') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergency_relation') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyRelation }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergency_phone') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyPhoneNumber }}</p>
              </div>
            </div>
            <!--residence card-->
            <div class="grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32 mt-5">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.front_card') }}</p>
                <img class="w-full lg:max-w-60" :src="user.frontCardUrl" alt="" />
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.back_card') }}</p>
                <img class="w-full lg:max-w-60" :src="user.backCardUrl" alt="" />
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.period_of_stay') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.periodOfStay }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.period_type') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.periodType }}</p>
              </div>
              <div v-if="user.periodType === 'STUDENT'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.school_name') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.schoolName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ user.periodType === 'STUDENT' ? t('models/residence_card.field.studentCard') : t('models/residence_card.field.front_side_identification') }}
                </p>
                <img class="w-full lg:max-w-60" :src="user.frontIdentificationUrl" alt="" />
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ user.periodType === 'STUDENT' ? t('models/residence_card.field.studentCard') : t('models/residence_card.field.back_side_identification') }}
                </p>
                <img class="w-full lg:max-w-60" :src="user.backIdentificationUrl" alt="" />
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.period_expire_at') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.periodExpireAt }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passport_image') }}</p>
                <img class="w-full lg:max-w-60" :src="user.passportUrl" alt="" />
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passport_number') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.passportNumber }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passport_expired_at') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.passportExpiredAt }}</p>
              </div>
            </div>
            <!--Bank info-->
            <div v-if="user.bankType == 'BANK'" class="w-full grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32 mt-5">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.bank_type') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.bankType }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.atm') }}</p>
                <img class="w-full lg:max-w-60" :src="user.atmCardUrl" alt="" />
              </div>
              <div v-show="user.bankType === 'BANK'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.bank_name') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.bankNameCode + ' - ' + user.bankName }}</p>
              </div>
              <div v-show="user.bankType === 'BANK'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.bank_branch') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.bankBranchCode + ' - ' + user.bankBranch }}</p>
              </div>
              <div v-show="user.bankType === 'BANK'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.deposit_type') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.depositType }}</p>
              </div>
              <div v-show="user.bankType === 'BANK'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.account_name') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.accountName }}</p>
              </div>
              <div v-show="user.bankType === 'BANK'">
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.account_number') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.accountNumber }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <UserBaseForm
    :is-open="isEditUserBase"
    :user="user"
    @close="isEditUserBase = false"
    @submit="handleSubmitUserBase"
  />
  <UserBankForm
    :is-open="isEditUserBank"
    :user="user"
    @close="isEditUserBank = false"
    @submit="handleSubmitUserBank"
  />
  </div>
</template>
