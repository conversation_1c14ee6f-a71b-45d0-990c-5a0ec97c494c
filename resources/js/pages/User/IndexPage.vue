<script setup lang="ts">
import { Head, Link, router, useForm } from "@inertiajs/vue3";
import VInput from '@/components/common/shared/VInput.vue';
import { route } from 'ziggy-js';
import VRadio from '@/components/common/shared/VRadio.vue';
import { TrashIcon, PencilSquareIcon } from '@heroicons/vue/24/solid/index.js';
import VPagination from '@/components/common/shared/VPagination.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import Button from '@/components/common/shared/Button.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import { ListUserProps, UserType } from "@/types/user.ts";
import { useI18n } from '@/composables/useI18n.ts';
import { computed, ref } from "vue";
import LoadingButton from "@/components/common/shared/LoadingButton.vue";
import ConfirmModal from "@/components/common/shared/ConfirmModal.vue";
import UserDetailPreview from "@/pages/User/common/DetailPage.vue";
import axios from "axios";

defineProps<ListUserProps>();

const { t } = useI18n();
const isModalDetail = ref(false);
const selectedUser = ref<UserType | null>(null);

const searchForm = useForm({
  keyword: '',
  prefecture: '',
  user_status: '',
  job_participation_count: '',
  residence_card_status: '',
  age_start: '',
  age_end: '',
  gender: '',
});

const genderOptions = window.genderOptions;
const userStatusOptions = window.userStatusOptions;
const residenceCardStatusOptions = window.residenceCardStatusOptions;

const prefectureOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  ...(window as any).prefectureOptions,
]);

function submitSearch() {
  Object.entries(searchForm.data()).forEach(([key, value]) => {
    if (value == '' || value == null) {
      searchForm[key] = undefined;
    }
  });

  searchForm.get(route('admin.user.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.user.index'));
}

async function openModalDetail(userId: number) {
  try {
    const res = await axios.get('/admin/user/' + userId + '/edit');
    if (res.data.result_code == 200) {
      selectedUser.value = res.data.result_detail;
      isModalDetail.value = true;
    }
  } catch (error) {
    console.error(error);
  }
}

function deleteUser(userId: number) {
  isModalDetail.value = false;
  if (userId)
    router.delete(route('admin.user.delete', userId));
}
</script>

<template>
  <Head title="User" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/user.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('common.field.search') }}
          </h4>
          <form @submit.prevent="submitSearch">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-24">
              <div>
                <v-input
                  :label="t('models/user.search_field.name_email_phone')"
                  v-model="searchForm.keyword"
                  placeholder=""
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-select
                  :label="t('models/user.search_field.prefecture')"
                  v-model="searchForm.prefecture"
                  :options="prefectureOptions"
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-radio
                  :label="t('models/user.search_field.status')"
                  v-model="searchForm.user_status"
                  :options="userStatusOptions"
                  class="w-full text-sm"
                />
              </div>
              <div>
                <v-input
                  :label="t('models/user.search_field.job_participation_count')"
                  v-model="searchForm.job_participation_count"
                  placeholder=""
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-radio
                  :label="t('models/user.search_field.status_residence_card')"
                  v-model="searchForm.residence_card_status"
                  :options="residenceCardStatusOptions"
                  class="w-full text-sm"
                />
              </div>

              <div>
                <v-radio
                  :label="t('models/user.search_field.gender')"
                  v-model="searchForm.gender"
                  :options="genderOptions"
                  class="w-full text-sm"
                />
              </div>
              <div>
                <label class="mb-1.5 block text-sm font-medium text-gray-700">{{ t('models/user.search_field.age') }}</label>
                <div class="grid grid-cols-2 gap-4">
                  <v-input v-model="searchForm.age_start" placeholder="" class="w-full" :required="false" />
                  <v-input v-model="searchForm.age_end" placeholder="" class="w-full" :required="false" />
                </div>
              </div>
            </div>
            <div class="flex items-center justify-center gap-2 mt-8">
              <loading-button
                :loading="searchForm.processing"
                class="w-28 h-9 justify-center text-sm font-medium text-white transition rounded-lg bg-brand-500 shadow-theme-xs hover:bg-brand-600"
                type="submit"
              >
                {{ t('common.btn.search') }}
              </loading-button>
              <Button variant="outline" class="h-9 w-28" type="button" @click="resetSearch">{{
                t('common.btn.reset')
              }}</Button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex items-center justify-end mb-6">
        <button-link size="sm" variant="primary" :href="route('admin.user.create')">{{
          t('common.btn.create')
        }}</button-link>
      </div>
      <div class="overflow-x-auto rounded-xl border border-gray-200 bg-white">
        <table class="divide-y divide-gray-200 min-w-[1300px] md:min-w-[1500px] xl:min-w-full">
          <thead class="bg-gray-50">
            <tr class="border-b border-gray-200">
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.id') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.name') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.phone_number') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.gender') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.birthday') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.prefecture') }}
              </th>

              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.email') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.user_status') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.search_field.status_residence_card') }}
              </th>
              <th scope="col" class="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('common.field.action') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="user in users.data" :key="user.id" class="border-t border-gray-100">
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.id }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.name }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.phoneNumber }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.genderName }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.age }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.prefecture }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.email }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.userStatusName }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ user.residenceCardStatus }}</span>
              </td>
              <td class="px-5 py-4">
                <div class="flex text-center">
                  <Link :href="route('admin.user.show', user.id)"><pencil-square-icon class="size-5 mr-2" /></Link>
                  <trash-icon class="size-5 mr-2 text-red-600 cursor-pointer" @click="openModalDetail(user.id)" />
                </div>
              </td>
            </tr>
            <tr v-if="users.data.length === 0">
              <td class="px-6 py-4 border-t" colspan="9">{{ t('common.noResult') }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2" v-if="users.data.length > 0">
        <v-pagination :paginator="users.paginator" />
      </div>
    </div>
  </div>

  <ConfirmModal
    v-model="isModalDetail"
    :saveButtonText="t('common.btn.delete')"
    :title="t('models/user.screenName.detail')"
    size="3xl"
    saveButtonVariant="danger"
    @close="isModalDetail = false"
    @save="deleteUser(selectedUser?.id)"
    :cancelButtonText="t('common.btn.back')"
  >
    <UserDetailPreview :user="selectedUser" />
  </ConfirmModal>


</template>

<style scoped></style>
