<script setup lang="ts">
import { Head } from "@inertiajs/vue3";
import type { InertiaForm } from '@inertiajs/vue3';
import { PencilSquareIcon } from "@heroicons/vue/24/solid";
import ButtonLink from "@/components/common/shared/ButtonLink.vue";
import { route } from "ziggy-js";
import { DetailUserProps, UserBankFormType, UserBaseFormType } from "@/types/user";
import { useI18n } from "@/composables/useI18n";
import { ref } from "vue";
import UserBaseForm from "@/components/user/UserBaseForm.vue";
import UserBankForm from "@/components/user/UserBankForm.vue";

const { user } = defineProps<DetailUserProps>();
const { t } = useI18n();
const isEditUserBase = ref(false);
const isEditUserBank = ref(false);
const isEditResidence = ref(false);

const handleSubmitUserBase = (form: InertiaForm<UserBaseFormType>) => {
  form.patch(route('admin.user.update.base', user.id), {
    onSuccess: () => {
      isEditUserBase.value = false;
    }
  });
};

const handleSubmitUserBank = (form: InertiaForm<UserBankFormType>) => {
  form.patch(route('admin.user.update.bank', user.id), {
    onSuccess: () => {
      isEditUserBank.value = false;
    }
  });
};
</script>

<template>
  <Head :title="t('models/user.title')"/>
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/user.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div class="w-full">
            <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
              {{ t('models/user.cardTitle.baseInfo') }}
            </h4>
            <div class="flex items-center space-x-4 mb-6">
              <img
                :src="user.avatarUrl || '/images/ava-default.png'"
                alt="Avatar"
                class="w-24 h-24 rounded-full object-cover"
              >
              <div>
                <p class="text-lg font-medium text-gray-800">{{ user.name }}</p>
                <p class="text-sm text-gray-600">{{ user.email }}</p>
              </div>
            </div>

            <div class="grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.code') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.code }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.nameKana') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nameKana }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.nameKanji') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nameKanji }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.healthCertificate') }}</p>
                <div class="text-sm font-medium text-gray-800">
                  <img class="w-full max-w-60" :src="user.healthCertificateUrl" alt="" />
                </div>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.phoneNumber') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.phoneNumber }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.gender') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.genderName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.birthday') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.birthday }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.nationality') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.nationalityName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.japaneseLevel') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.japaneseLevelName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.arrivalDate') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.arrivalDate }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.zipcode') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.zipCode }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.prefecture') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.prefecture }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.streetAddress') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.streetAddress }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.townAddress') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.townAddress }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.trainStationName') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.trainStationName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergencyName') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyName }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergencyRelation') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyRelation }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.emergencyPhone') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ user.emergencyPhoneNumber }}</p>
              </div>
            </div>
          </div>
          <button class="edit-button" @click="isEditUserBase = true">
            <PencilSquareIcon class="w-5 h-5"/>
            {{ t('common.btn.edit') }}
          </button>
        </div>
      </div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div class="w-full">
            <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
              {{ t('models/user.cardTitle.residencePassport') }}
            </h4>
            <div class="flex items-center space-x-4 mb-6">
              <div class="grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32">
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.frontCard') }}</p>
                  <img class="w-full lg:max-w-60" :src="user.frontCardUrl" alt="" />
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.backCard') }}</p>
                  <img class="w-full lg:max-w-60" :src="user.backCardUrl" alt="" />
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.periodOfStay') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.periodOfStay }}</p>
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.periodType') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.periodType }}</p>
                </div>
                <div v-if="user.periodType === 'STUDENT'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.schoolName') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.schoolName }}</p>
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">
                    {{ user.periodType === 'STUDENT' ? t('models/residence_card.field.studentCard') : t('models/residence_card.field.identification') }}
                  </p>
                  <img class="w-full lg:max-w-60" :src="user.identificationUrl" alt="" />
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/residence_card.field.periodExpireAt') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.periodExpireAt }}</p>
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passportImage') }}</p>
                  <img class="w-full lg:max-w-60" :src="user.passportUrl" alt="" />
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passportNumber') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.passportNumber }}</p>
                </div>
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.passportExpiredAt') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.passportExpiredAt }}</p>
                </div>
              </div>
            </div>
          </div>
          <button class="edit-button" @click="isEditResidence = true">
            <PencilSquareIcon class="w-5 h-5"/>
            {{ t('common.btn.edit') }}
          </button>
        </div>
      </div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div class="w-full">
            <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
              {{ t('models/user.cardTitle.bankInfo') }}
            </h4>
            <div class="flex items-center space-x-4 mb-6">
              <div class="w-full grid grid-cols-1 gap-4 lg:gap-7 2xl:gap-x-32">
                <div>
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user.field.bankType') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.bankType }}</p>
                </div>
                <div v-show="user.bankType === 'BANK'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.bankName') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.bankName }}</p>
                </div>
                <div v-show="user.bankType === 'BANK'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.bankBranch') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.bankBranch }}</p>
                </div>
                <div v-show="user.bankType === 'BANK'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.depositType') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.depositType }}</p>
                </div>
                <div v-show="user.bankType === 'BANK'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.accountName') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.accountName }}</p>
                </div>
                <div v-show="user.bankType === 'BANK'">
                  <p class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/user_bank.field.accountNumber') }}</p>
                  <p class="text-sm font-medium text-gray-800">{{ user.accountNumber }}</p>
                </div>
              </div>
            </div>
          </div>
          <button class="edit-button" @click="isEditUserBank = true">
            <PencilSquareIcon class="w-5 h-5"/>
            {{ t('common.btn.edit') }}
          </button>
        </div>
      </div>

      <div class="flex justify-end">
        <ButtonLink size="sm" variant="outline" :href="route('admin.user.index')">{{ t('common.btn.back') }}</ButtonLink>
      </div>
    </div>
  </div>
  <UserBaseForm
    :is-open="isEditUserBase"
    :user="user"
    @close="isEditUserBase = false"
    @submit="handleSubmitUserBase"
  />
  <UserBankForm
    :is-open="isEditUserBank"
    :user="user"
    @close="isEditUserBank = false"
    @submit="handleSubmitUserBank"
  />
</template>
