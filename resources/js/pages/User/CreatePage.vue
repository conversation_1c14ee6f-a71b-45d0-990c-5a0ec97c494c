<script setup lang="ts">
import { Head, useForm } from "@inertiajs/vue3";
import UserForm from '@/components/user/UserForm.vue';
import { route } from 'ziggy-js';
import { UserFormType } from "@/types/user.ts";
import { useI18n } from "@/composables/useI18n.ts";

const { t } = useI18n();
const form = useForm<UserFormType>({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  name_kana: '',
  name_kanji: '',
  phone_number: '',
  gender: '',
  birthday: '',
  nationality: '',
  has_certificate: false,
  japanese_level: '',
  arrival_date: '',
  zip_code: '',
  prefecture: '',
  street_address: '',
  town_address: '',
  train_station_name: '',
  emergency_name: '',
  emergency_relation: '',
  emergency_phone_number: '',
  bank_type: 'CASH',
  avatar: undefined,
  health_certificate: undefined,
  front_card: undefined,
  back_card: undefined,
  identification: undefined,
  period_type: '',
  period_of_stay: '',
  passport_image: undefined,
  passport_number: '',
  passport_expired_at: '',
  school_name: '',
  period_expire_at: '',
  atm_image: undefined,
  bank_name: '',
  bank_branch: '',
  account_name: '',
  account_number: '',
  deposit_type: ''
});

function store() {
  form.post(route('admin.user.store'));
}
</script>

<template>
  <Head :title="t('models/user.title')"/>
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/user.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <UserForm :form="form" :is-editing="false" :user-id="null" @submit="store"/>
  </div>
</template>

<style scoped>

</style>
