<script setup lang="ts">
import { Head, usePage } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n';
import { ref } from 'vue';
import OnlineUsersCounter from '@/components/OnlineUsersCounter.vue';

const { t } = useI18n();
const page = usePage();
const onlineUsersData = ref(page.props.onlineUsersData || { total: 0, timestamp: new Date().toISOString() });
</script>

<template>
  <Head title="Dashboard" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/dashboard.screenName.index') }}
    </h2>
  </div>

  <!-- Online Users Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
    <OnlineUsersCounter :initial-data="onlineUsersData" />
  </div>
</template>

<style scoped></style>
