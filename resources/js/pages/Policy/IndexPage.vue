<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { EyeIcon, TrashIcon, PencilSquareIcon } from '@heroicons/vue/24/solid';
import VPagination from '@/components/common/shared/VPagination.vue';
import VInput from '@/components/common/shared/VInput.vue';
import Button from '@/components/common/shared/Button.vue';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import VCheckbox from '@/components/common/shared/VCheckbox.vue';
import { ListPolicyProps } from '@/types/policy.ts';

defineProps<ListPolicyProps>();

const { t } = useI18n();

const searchForm = useForm({
  title: '',
  is_public: false,
});

function submitSearch() {
  searchForm.get(route('admin.policy.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.policy.index'));
}

function deletePolicy(id: number) {
  if (confirm(t('common.confirm_delete'))) {
    router.delete(route('admin.policy.delete', id));
  }
}
</script>

<template>
  <Head :title="t('models/policy.title')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white/90">
      {{ t('models/policy.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('common.field.search') }}
          </h4>
          <form @submit.prevent="submitSearch">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
              <div>
                <v-input
                  :label="t('models/policy.field.title')"
                  v-model="searchForm.title"
                  placeholder="Enter title..."
                  class="w-full"
                  :required="false"
                />
              </div>
              <div>
                <v-checkbox :label="t('models/policy.field.is_public')" v-model="searchForm.is_public" class="w-full" />
              </div>
            </div>
            <div class="flex items-center justify-center gap-2 mt-6">
              <Button size="sm" variant="outline" type="button" @click="resetSearch">{{
                t('common.btn.reset')
              }}</Button>
              <Button size="sm" variant="primary" type="submit">{{ t('common.btn.search') }}</Button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end mb-6">
      <button-link size="sm" variant="primary" :href="route('admin.policy.create')">{{
        t('common.btn.create')
      }}</button-link>
    </div>
    <div class="overflow-hidden rounded-xl border border-gray-200 bg-white">
      <div class="max-w-full overflow-x-auto custom-scrollbar">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/policy.field.id') }}
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/policy.field.title') }}
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/policy.field.is_public') }}
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('common.createdAt') }}
              </th>
              <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('common.field.action') }}
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="policy in policies.data" :key="policy.id" class="border-t border-gray-100">
              <td class="px-5 py-4 sm:px-6">{{ policy.id }}</td>
              <td class="px-5 py-4 sm:px-6">{{ policy.title }}</td>
              <td class="px-5 py-4 sm:px-6">{{ policy.isPublic ? 'Yes' : 'No' }}</td>
              <td class="px-5 py-4 sm:px-6">{{ policy.createdAt }}</td>
              <td class="px-5 py-4 sm:px-6">
                <div class="flex items-center gap-2">
                  <Link :href="route('admin.policy.show', policy.id)"><EyeIcon class="w-5 h-5" /></Link>
                  <Link :href="route('admin.policy.edit', policy.id)"><PencilSquareIcon class="w-5 h-5" /></Link>
                  <TrashIcon
                    class="w-5 h-5 cursor-pointer text-red-500 hover:text-red-700"
                    @click="deletePolicy(policy.id)"
                  />
                </div>
              </td>
            </tr>
            <tr v-if="privacies.data.length === 0">
              <td colspan="5" class="px-3 py-4 text-sm text-center text-gray-500">
                {{ t('common.noResult') }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="mt-2" v-if="privacies.data.length > 0">
      <v-pagination :paginator="privacies.paginator" />
    </div>
  </div>
</template>

<style scoped></style>
