<script setup lang="ts">
import { DetailPolicyProps, PolicyFormType } from '@/types/policy.ts';
import { useI18n } from '@/composables/useI18n.ts';
import { ref } from 'vue';
import { Head, InertiaForm, useForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import EditForm from '@/pages/Policy/common/EditForm.vue';
import Modal from '@/components/common/Modal.vue';

const props = defineProps<DetailPolicyProps>();
const { t } = useI18n();
const isPreviewEdit = ref(false);

const formData = useForm<PolicyFormType>({
  title: props.policy.title,
  body: props.policy.body,
  is_public: props.policy.isPublic,
});
const update = (form: InertiaForm<PolicyFormType>) => {
  form.put(route('admin.policy.update', props.policy.id), {
    onSuccess: () => {
      isPreviewEdit.value = false;
    },
  });
};
</script>

<template>
  <Head :title="t('models/policy.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/policy.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <EditForm :form-data="formData" @openPreview="isPreviewEdit = true" />
  </div>
  <Modal v-if="isPreviewEdit" @close="isPreviewEdit = false" :title="t('models/policy.screenName.edit')">
    <template #body>
      <EditForm
        :form-data="formData"
        @submit="update"
        @openPreview="isPreviewEdit = true"
        @close="isPreviewEdit = false"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
