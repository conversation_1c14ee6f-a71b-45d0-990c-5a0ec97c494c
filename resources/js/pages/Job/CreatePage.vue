<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { route } from 'ziggy-js';
import { CreateJobProps, JobFormType } from '@/types/job.ts';
import JobForm from '@/pages/Job/common/JobForm.vue';
import Modal from '@/components/common/Modal.vue';
import { useValidation } from '@/composables/useValidation';

defineProps<CreateJobProps>();

const { t } = useI18n();

const {
  form,
  isPreview: isPreviewCreate,
  validateBeforePreview,
  setPreview,
  showConfirmModal,
  confirmLeave,
  cancelLeave,
  markAsClean,
  markAsSubmitting,
  resetSubmitting,
} = useValidation<JobFormType>({
  initialValues: {
    group_id: '',
    is_recommended: false,
    recruitment_type: '',
    thumbnail: null,
    images: [],
    category_id: '',
    employer_email: '',
    employer_name: '',
    employer_phone_number: '',
    type: '',
    prefecture: '',
    address: '',
    title: '',
    description: '',
    benefits: '',
    certificate_level: '',
    time_start: '',
    time_end: '',
    salary_type: '',
    salary: '',
    travel_fee_type: '',
    travel_fee: '',
    age: '',
    gender: '',
    quantity: '',
    recruit_start_at: null,
    recruit_expired_at: null,
    job_start_at: null,
    is_instant: false,
    is_public: false,
    deleted_image_ids: [],
    existing_images: [],
  },
  labels: {
    group_id: t('models/job.field.group_id'),
    is_recommended: t('models/job.field.is_recommended'),
    recruitment_type: t('models/job.field.recruitment_type'),
    thumbnail: t('models/job.field.thumbnail'),
    images: t('models/job.field.images'),
    category_id: t('models/job.field.category'),
    employer_email: t('models/job.field.employer_email'),
    employer_name: t('models/job.field.employer_name'),
    employer_phone_number: t('models/job.field.employer_phone_number'),
    type: t('models/job.field.type'),
    prefecture: t('models/job.field.prefecture'),
    address: t('models/job.field.address'),
    title: t('models/job.field.title'),
    description: t('models/job.field.description'),
    benefits: t('models/job.field.benefits'),
    certificate_level: t('models/job.field.certificate_level'),
    time_start: t('models/job.field.time_start'),
    time_end: t('models/job.field.time_end'),
    salary_type: t('models/job.field.salary_type'),
    salary: t('models/job.field.salary'),
    travel_fee_type: t('models/job.field.travel_fee_type'),
    travel_fee: t('models/job.field.travel_fee'),
    age: t('models/job.field.age'),
    gender: t('models/job.field.gender'),
    quantity: t('models/job.field.quantity'),
    recruit_start_at: t('models/job.field.recruit_start_at'),
    recruit_expired_at: t('models/job.field.recruit_expired_at'),
    job_start_at: t('models/job.field.job_start_at'),
  },
  enableUnsavedChanges: true,
  unsavedChangesOptions: {
    excludeFields: ['thumbnail', 'images'],
  },
});

function handlePreview() {
  validateBeforePreview({
    group_id: ['required'],
    is_recommended: ['required'],
    recruitment_type: ['required'],
    thumbnail: ['required'],
    images: ['required'],
    category_id: ['required'],
    employer_email: ['required'],
    employer_name: ['required'],
    employer_phone_number: ['required'],
    type: ['required'],
    prefecture: ['required'],
    address: ['required'],
    title: ['required'],
    description: ['required'],
    benefits: ['optional'],
    certificate_level: ['optional'],
    time_start: ['required'],
    time_end: ['required'],
    salary_type: ['required'],
    salary: ['required'],
    travel_fee_type: ['required'],
    travel_fee: ['required'],
    age: ['required'],
    gender: ['required'],
    quantity: ['required'],
    recruit_start_at: ['required'],
    recruit_expired_at: ['required'],
    job_start_at: ['required'],
  });
}

function store() {
  isPreviewCreate.value = false;
  markAsSubmitting?.();

  form.post(route('admin.job.store'), {
    onSuccess: () => {
      markAsClean?.();
    },
    onError: () => {
      resetSubmitting?.();
    },
  });
}
</script>

<template>
  <Head :title="t('models/job.screenName.create')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <JobForm
      :categories="categories"
      :groups="groups"
      :form-data="form"
      @openPreview="handlePreview()"
      :show-confirm-modal="showConfirmModal"
      :confirm-leave="confirmLeave"
      :cancel-leave="cancelLeave"
    />
  </div>
  <Modal v-if="isPreviewCreate" @close="setPreview(false)" :title="t('models/job.screenName.create')">
    <template #body>
      <JobForm
        :groups="groups"
        :categories="categories"
        :form-data="form"
        @submit="store"
        @openPreview="isPreviewCreate = true"
        @close="setPreview(false)"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>

<style scoped></style>
