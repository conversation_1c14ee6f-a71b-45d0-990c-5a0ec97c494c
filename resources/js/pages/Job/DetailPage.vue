<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { DetailJobProps } from '@/types/job';
import { useI18n } from '@/composables/useI18n';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import { PencilSquareIcon } from '@heroicons/vue/24/solid';

const { job } = defineProps<DetailJobProps>();
const { t } = useI18n();
</script>

<template>
  <Head :title="t('models/job.screenName.detail')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div>
      <div class="relative p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <ButtonLink
          :href="route('admin.job.edit', job.id)"
          class="edit-button absolute top-5 right-5 lg:top-6 lg:right-6"
        >
          <PencilSquareIcon class="w-5 h-5" />
          {{ t('common.btn.edit') }}
        </ButtonLink>

        <div>
          <div class="grid grid-cols-1 gap-4 lg:gap-7">
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.title') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.title }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.description') }}
              </p>
              <p class="text-sm font-medium text-gray-800 whitespace-pre">{{ job.description }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.group_id') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.groupName }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.is_recommended') }}
              </p>
              <p class="text-sm font-medium text-gray-800 whitespace-pre">{{ job.isRecommended }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.recruitment_type') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.recruitmentType }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.thumbnail') }}
              </p>
              <img class="w-full max-w-36" :src="job.thumbnailUrl" alt="" />
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.images') }}
              </p>
              <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <img
                  v-for="image in job.images"
                  :key="image.id"
                  :src="image.imageUrl"
                  alt="Image"
                  class="w-full sm:h-36"
                />
              </div>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.category') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.categoryName }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.employer_name') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.employerName }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.employer_email') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.employerEmail }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.employer_phone_number') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.employerPhoneNumber }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.type') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.type }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.prefecture') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.prefecture }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.address') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.address }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.benefits') }}
              </p>
              <p class="text-sm font-medium text-gray-800 whitespace-pre">{{ job.benefits }}</p>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.time_start') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.timeStart }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.time_end') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.timeEnd }}</p>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.salary_type') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.salaryType }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.salary') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.salary }}</p>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.travel_fee_type') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.travelFeeType }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.travel_fee') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.travelFee }}</p>
              </div>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.age') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.age }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.gender') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.gender }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.quantity') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.quantity }}</p>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.recruit_start_at') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.recruitStartAt }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500">
                  {{ t('models/job.field.recruit_expired_at') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ job.recruitExpiredAt }}</p>
              </div>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.job_start_at') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.jobStartAt }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.is_instant') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.isInstant ? t('common.yes') : t('common.no') }}</p>
            </div>
            <div>
              <p class="mb-2 text-xs leading-normal text-gray-500">
                {{ t('models/job.field.is_public') }}
              </p>
              <p class="text-sm font-medium text-gray-800">{{ job.isPublic ? t('common.yes') : t('common.no') }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <ButtonLink size="sm" variant="outline" :href="route('admin.job.index')">
          {{ t('common.btn.back') }}
        </ButtonLink>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
