<script setup lang="ts">
import { useI18n } from '@/composables/useI18n.ts';
import { JobType } from '@/types/job.ts';

const { t } = useI18n();

interface Props {
  job: JobType | null;
}

defineProps<Props>();
</script>

<template>
  <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
    <div class="border-b pb-4">
      <div class="grid grid-cols-1 gap-4">
        <div>
          <p class="text-sm text-gray-500">{{ t('models/job.field.title') }}</p>
          <p class="font-medium">{{ job?.title }}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">{{ t('models/job.field.category') }}</p>
          <p class="font-medium">{{ job?.categoryName }}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">{{ t('models/job.field.employer_name') }}</p>
          <p class="font-medium">{{ job?.employerName }}</p>
        </div>
        <div>
          <p class="text-sm text-gray-500">{{ t('models/job.field.employer_email') }}</p>
          <p class="font-medium">{{ job?.employerEmail }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
