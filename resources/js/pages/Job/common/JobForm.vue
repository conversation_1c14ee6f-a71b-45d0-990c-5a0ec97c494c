<script setup lang="ts">
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import { useI18n } from '@/composables/useI18n.ts';
import { InertiaForm } from '@inertiajs/vue3';
import { JobCategoryType, JobFormType } from '@/types/job.ts';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import VSelect from '@/components/common/shared/VSelect.vue';
import VFileInput from '@/components/common/shared/VFileInput.vue';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import VRadio from '@/components/common/shared/VRadio.vue';
import UnsavedChangesModal from '@/components/common/UnsavedChangesModal.vue';
import { computed, ref, onMounted } from 'vue';
import CkEditor from '@/components/common/shared/CkEditor.vue';
import { GroupType } from '@/types/group.ts';

interface ImageItem {
  id: number;
  imageUrl: string;
}

const props = defineProps<{
  isPreview?: boolean;
  formData: InertiaForm<JobFormType>;
  isEdit?: boolean;
  categories: JobCategoryType[];
  groups: GroupType[];
  existingThumbnail?: string;
  existingImages?: ImageItem[];
  deletedImageIds?: number[];
  // Unsaved changes props
  showConfirmModal?: boolean;
  confirmLeave?: () => void;
  cancelLeave?: () => void;
}>();

const emit = defineEmits<{
  (e: 'openPreview'): void;
  (e: 'close'): void;
  (e: 'submit', form: InertiaForm<JobFormType>): void;
  (e: 'deleteImage', imageId: number): void;
}>();

const { t } = useI18n();
const form = props.formData;

const recruitmentTypeOptions = (window as any).recruitmentTypeOptions;
const jobTypeOptions = (window as any).jobTypeOptions;
const genderOptions = (window as any).genderOptions;
const certificateLevelOptions = (window as any).standardCertificateLevels;
const prefectureOptions = (window as any).prefectureOptions;
const feeTypeOptions = (window as any).feeTypeOptions;
const categoriesOptions = computed(() => [
  ...props.categories.map((category: any) => ({
    label: category.name,
    value: category.id,
  })),
]);
const booleanOptions = (window as any).booleanOptions;
const groupsOption = computed(() => [
  ...props.groups.map((group: GroupType) => ({
    label: `${group.companyName} - ${group.siteName}`,
    value: group.id,
  })),
]);

const thumbnailPreview = ref<string | null>(null);
const imagesPreview = ref<string[]>([]);

const visibleExistingImages = computed(() => {
  if (!props.existingImages) return [];
  return props.existingImages.filter(image => !props.deletedImageIds?.includes(image.id));
});

const handleDeleteExistingImage = (imageId: number) => {
  emit('deleteImage', imageId);
};

const createImagePreviews = () => {
  if (!props.isPreview) return;

  if (form.thumbnail && form.thumbnail instanceof File) {
    const reader = new FileReader();
    reader.onload = e => {
      thumbnailPreview.value = e.target?.result as string;
    };
    reader.readAsDataURL(form.thumbnail);
  }

  if (form.images && Array.isArray(form.images)) {
    imagesPreview.value = [];
    form.images.forEach((file: File, index: number) => {
      if (file instanceof File) {
        const reader = new FileReader();
        reader.onload = e => {
          imagesPreview.value[index] = e.target?.result as string;
        };
        reader.readAsDataURL(file);
      }
    });
  }
};

onMounted(() => {
  if (props.isPreview) {
    createImagePreviews();
  }
});

function handleSubmit() {
  emit('submit', form);
}
</script>

<template>
  <form class="flex flex-col" @submit.prevent="handleSubmit">
    <div :class="isPreview ? 'overflow-y-auto custom-scrollbar max-h-[65vh]' : ''">
      <div class="grid grid-cols-2 gap-x-6 gap-y-5 pr-2">
        <v-input
          v-model="form.title"
          :error="form.errors.title"
          class="w-full col-span-2"
          :label="t('models/job.field.title')"
          required
          :disabled="isPreview"
        />
        <ck-editor
          v-if="!isPreview"
          v-model="form.description"
          :error="form.errors.description"
          class="col-span-2"
          :label="t('models/job.field.description')"
          required
        />
        <v-select
          v-model="form.group_id"
          :error="form.errors.group_id"
          :options="groupsOption"
          class="w-full col-span-2"
          :label="t('models/job.field.group_id')"
          required
          :disabled="isPreview"
        />
        <v-radio
          v-model="form.is_recommended"
          :error="form.errors.is_recommended"
          :options="booleanOptions"
          class="w-full"
          :label="t('models/job.field.is_recommended')"
          :disabled="isPreview"
        />
        <v-select
          v-model="form.recruitment_type"
          :error="form.errors.recruitment_type"
          :options="recruitmentTypeOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.recruitment_type')"
          required
          :disabled="isPreview"
        />
        <div class="w-full col-span-2">
          <div v-if="isEdit && props.existingThumbnail && !isPreview" class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('models/job.field.thumbnail') }}
            </label>
            <div class="rounded-lg">
              <img
                v-if="!form.thumbnail"
                :src="props.existingThumbnail"
                :alt="t('models/job.field.thumbnail')"
                class="h-24 object-cover rounded"
              />
            </div>
          </div>
          <div v-if="isPreview">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ t('models/job.field.thumbnail') }}
              <span class="text-red-500">*</span>
            </label>
            <div>
              <img
                :src="thumbnailPreview ? thumbnailPreview : props.existingThumbnail"
                :alt="t('models/job.field.thumbnail')"
                class="max-w-full h-24 object-contain rounded"
              />
            </div>
          </div>
          <v-file-input
            v-if="!isPreview"
            v-model="form.thumbnail"
            :error="form.errors.thumbnail"
            accept="image/*"
            class="w-full"
            :label="isEdit ? '' : t('models/job.field.thumbnail')"
            :required="!isEdit"
          />
        </div>
        <div class="w-full col-span-2">
          <div v-if="isEdit && visibleExistingImages.length > 0 && !isPreview" class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              {{ t('models/job.field.images') }}
            </label>
            <div>
              <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div
                  v-for="image in visibleExistingImages"
                  :key="image.id"
                  class="relative group border rounded-lg bg-white hover:shadow-md transition-shadow"
                >
                  <img :src="image.imageUrl" :alt="`Image ${image.id}`" class="w-full h-32 object-cover rounded" />

                  <div
                    class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100"
                  >
                    <button
                      @click="handleDeleteExistingImage(image.id)"
                      class="p-2 bg-red-500 rounded-full shadow-lg hover:bg-red-600 transition-colors"
                      :title="t('common.btn.delete')"
                    >
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="isPreview">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              {{ t('models/job.field.images') }}
              <span v-if="!isEdit" class="text-red-500">*</span>
            </label>
            <div v-if="imagesPreview.length > 0">
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div v-for="(preview, index) in imagesPreview" :key="index" class="rounded-lg">
                  <img :src="preview" :alt="`Image ${index + 1}`" class="h-24 object-contain rounded" />
                </div>
              </div>
            </div>
            <div v-else class="border rounded-lg p-4 bg-gray-50 text-center text-gray-500">No images selected</div>
          </div>

          <v-file-input
            v-if="!isPreview"
            v-model="form.images"
            multiple
            :error="form.errors.images"
            accept="image/*"
            class="w-full"
            :label="isEdit ? '' : t('models/job.field.images')"
            :required="!isEdit"
          />
        </div>
        <v-select
          v-model="form.category_id"
          :error="form.errors.category_id"
          :options="categoriesOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.category')"
          :disabled="isPreview"
          required
        />
        <v-input
          v-model="form.employer_name"
          :error="form.errors.employer_name"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_name')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.employer_email"
          :error="form.errors.employer_email"
          type="email"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_email')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.employer_phone_number"
          :error="form.errors.employer_phone_number"
          class="w-full col-span-2"
          :label="t('models/job.field.employer_phone_number')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.type"
          :error="form.errors.type"
          :options="jobTypeOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.type')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.prefecture"
          :error="form.errors.prefecture"
          :options="prefectureOptions"
          class="w-full"
          :label="t('models/job.field.prefecture')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.address"
          :error="form.errors.address"
          class="w-full"
          :label="t('models/job.field.address')"
          required
          :disabled="isPreview"
        />
        <ck-editor
          v-if="!isPreview"
          v-model="form.benefits"
          :error="form.errors.benefits"
          class="col-span-2"
          :label="t('models/job.field.benefits')"
        />
        <v-select
          v-model="form.certificate_level"
          :error="form.errors.certificate_level"
          :options="certificateLevelOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.certificate_level')"
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.time_start"
          :error="form.errors.time_start"
          type="time"
          class="w-full"
          :label="t('models/job.field.time_start')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.time_end"
          :error="form.errors.time_end"
          type="time"
          class="w-full"
          :label="t('models/job.field.time_end')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.salary_type"
          :error="form.errors.salary_type"
          :options="feeTypeOptions"
          class="w-full"
          :label="t('models/job.field.salary_type')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.salary"
          :error="form.errors.salary"
          class="w-full"
          type="number"
          min="0"
          :label="t('models/job.field.salary')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.travel_fee_type"
          :error="form.errors.travel_fee_type"
          :options="feeTypeOptions"
          class="w-full"
          :label="t('models/job.field.travel_fee_type')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.travel_fee"
          :error="form.errors.travel_fee"
          class="w-full"
          type="number"
          min="0"
          :label="t('models/job.field.travel_fee')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.age"
          :error="form.errors.age"
          class="w-full col-span-2"
          type="number"
          min="0"
          :label="t('models/job.field.age')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.gender"
          :error="form.errors.gender"
          :options="genderOptions"
          class="w-full col-span-2"
          :label="t('models/job.field.gender')"
          :disabled="isPreview"
        />
        <v-input
          v-model="form.quantity"
          :error="form.errors.quantity"
          class="w-full col-span-2"
          type="number"
          min="0"
          :label="t('models/job.field.quantity')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.recruit_start_at"
          :error="form.errors.recruit_start_at"
          type="datetime"
          class="w-full"
          :label="t('models/job.field.recruit_start_at')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.recruit_expired_at"
          :error="form.errors.recruit_expired_at"
          type="datetime"
          class="w-full"
          :label="t('models/job.field.recruit_expired_at')"
          required
          :disabled="isPreview"
        />
        <date-picker
          v-model="form.job_start_at"
          :error="form.errors.job_start_at"
          type="datetime"
          class="w-full col-span-2"
          :label="t('models/job.field.job_start_at')"
          required
          :disabled="isPreview"
        />
        <v-radio
          v-model="form.is_instant"
          :error="form.errors.is_instant"
          :options="booleanOptions"
          class="w-full"
          :label="t('models/job.field.is_instant')"
          :disabled="isPreview"
        />
        <v-radio
          v-model="form.is_public"
          :error="form.errors.is_public"
          :options="booleanOptions"
          class="w-full"
          :label="t('models/job.field.is_public')"
          :disabled="isPreview"
        />
      </div>
    </div>
    <div v-if="isPreview" class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="primary" type="submit">
        {{ t('common.btn.save') }}
      </Button>
      <Button size="sm" variant="outline" type="button" @click="emit('close')">
        {{ t('common.btn.cancel') }}
      </Button>
    </div>
    <div v-else class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="outline" @click="emit('openPreview')">
        {{ t('common.btn.preview') }}
      </Button>
      <ButtonLink :href="route('admin.job.index')" size="sm" variant="outline">
        {{ t('common.btn.cancel') }}
      </ButtonLink>
    </div>
  </form>

  <UnsavedChangesModal :show="!!showConfirmModal" @confirm="confirmLeave" @cancel="cancelLeave" />
</template>

<style scoped></style>
