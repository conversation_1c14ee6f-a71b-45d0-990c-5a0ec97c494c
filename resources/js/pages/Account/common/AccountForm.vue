<script setup lang="ts">
import { AccountEditFormType } from '@/types/account.ts';
import { useI18n } from '@/composables/useI18n.ts';
import { InertiaForm } from '@inertiajs/vue3';
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';

const props = defineProps<{
  isEdit?: boolean;
  isPreview?: boolean;
  formData: InertiaForm<AccountEditFormType>;
}>();

const roleOptions = (window as any).accountRole;

const emit = defineEmits<{
  (e: 'openPreview'): void;
  (e: 'submit', form: InertiaForm<AccountEditFormType>): void;
  (e: 'close'): void;
}>();

const { t } = useI18n();

const form = props.formData;

const handleSubmit = () => {
  emit('submit', form);
};
</script>

<template>
  <form class="flex flex-col" @submit.prevent="handleSubmit" v-if="form">
    <div :class="isPreview ? 'overflow-y-auto custom-scrollbar max-h-[65vh]' : ''">
      <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
        <v-input
          v-if="!isEdit"
          v-model="form.login_id"
          :error="form.errors.login_id"
          class="w-full"
          :label="t('models/account.field.login_id')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.name"
          :error="form.errors.name"
          class="w-full"
          :label="t('models/account.field.name')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-model="form.email"
          :error="form.errors.email"
          class="w-full"
          :label="t('models/account.field.email')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-if="!isEdit"
          v-model="form.password"
          :error="form.errors.password"
          type="password"
          class="pb-6 w-full"
          :label="t('models/account.field.password')"
          required
          :disabled="isPreview"
        />
        <v-input
          v-if="!isEdit"
          v-model="form.password_confirmation"
          :error="form.errors.password_confirmation"
          type="password"
          class="pb-6 w-full"
          :label="t('models/account.field.password_confirmation')"
          required
          :disabled="isPreview"
        />
        <v-select
          v-model="form.role_div"
          :error="form.errors.role_div"
          :options="roleOptions"
          class="pb-6 w-full"
          :label="t('models/account.field.role_div')"
          required
          :disabled="isPreview"
        />
      </div>
    </div>
    <div v-if="isPreview" class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="primary" type="submit">{{ t('common.btn.save') }}</Button>
      <Button size="sm" variant="outline" type="button" @click="emit('close')">{{ t('common.btn.cancel') }}</Button>
    </div>
    <div v-else class="flex justify-center mt-4">
      <Button class="mr-2" size="sm" variant="outline" @click="emit('openPreview')">{{
        t('common.btn.preview')
      }}</Button>
      <ButtonLink :href="route('admin.account.index')" size="sm" variant="outline">{{
        t('common.btn.cancel')
      }}</ButtonLink>
    </div>
  </form>
</template>
