<script setup lang="ts">
import { Head, InertiaForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import { AccountEditFormType, DetailAccountProps } from '@/types/account.ts';
import Modal from '@/components/common/Modal.vue';
import AccountForm from '@/pages/Account/common/AccountForm.vue';
import { useValidation } from '@/composables/useValidation';

const props = defineProps<DetailAccountProps>();

const { t } = useI18n();

const {
  form: formData,
  isPreview: isPreviewEdit,
  validateBeforePreview,
  setPreview,
} = useValidation<AccountEditFormType>({
  name: props.account.name,
  email: props.account.email,
  role_div: props.account.roleDiv,
});

const update = (form: InertiaForm<AccountEditFormType>) => {
  form.put(route('admin.account.update', props.account.id), {
    onSuccess: () => {
      setPreview(false);
    },
  });
};

function handlePreview() {
  validateBeforePreview({
    name: ['required'],
    email: ['required', 'email', 'max:100'],
    role_div: ['required'],
  });
}
</script>

<template>
  <Head :title="t('models/account.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/account.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <AccountForm :is-edit="true" :form-data="formData" @openPreview="handlePreview()" />
  </div>
  <Modal
    v-if="isPreviewEdit"
    @close="setPreview(false)"
    :title="t('models/account.screenName.edit')"
  >
    <template #body>
      <AccountForm
        :is-edit="true"
        :form-data="formData"
        @submit="update"
        @openPreview="handlePreview()"
        @close="setPreview(false)"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>
