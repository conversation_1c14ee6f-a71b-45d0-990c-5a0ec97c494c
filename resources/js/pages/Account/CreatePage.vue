<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import AccountForm from '@/pages/Account/common/AccountForm.vue';
import Modal from '@/components/common/Modal.vue';
import { useValidation } from '@/composables/useValidation';

const { t } = useI18n();

const {
  form,
  isPreview: isPreviewCreate,
  validateBeforePreview,
  setPreview,
} = useValidation({
  login_id: '',
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  role_div: 'NORMAL',
});

function store() {
  form.post(route('admin.account.store'));
}

function handlePreview() {
  validateBeforePreview({
    login_id: ['required'],
    name: ['required'],
    email: ['required', 'max:100'],
    password: ['required', 'max:255'],
    password_confirmation: ['required'],
    role_div: ['required'],
  });
}
</script>

<template>
  <Head title="Account" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/account.screenName.create') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <AccountForm :form-data="form" @openPreview="handlePreview()" />
  </div>
  <Modal
    v-if="isPreviewCreate"
    @close="setPreview(false)"
    :title="t('models/account.screenName.create')"
  >
    <template #body>
      <AccountForm
        :form-data="form"
        @submit="store"
        @openPreview="setPreview(true)"
        @close="setPreview(false)"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>

<style scoped></style>
