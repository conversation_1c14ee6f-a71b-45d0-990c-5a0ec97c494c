<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { PencilSquareIcon } from '@heroicons/vue/24/solid';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import VSelect from '@/components/common/shared/VSelect.vue';
import { DetailAccountProps } from '@/types/account.ts';

const props = defineProps<DetailAccountProps>();

const { t } = useI18n();

const approvalStatusOptions = window.approvalStatusOptions;
const formApproval = useForm({
  approval_status: props.account.approvalStatus,
});

function updateStatus() {
  formApproval.patch(route('admin.account.approval', props.account.id));
}
</script>

<template>
  <Head title="Account" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/account.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div>
            <div class="grid grid-cols-1 gap-4 lg:gap-7">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/account.field.loginId') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ account.loginId }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/account.field.name') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ account.name }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/account.field.email') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ account.email }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                  {{ t('models/account.field.roleDiv') }}
                </p>
                <p class="text-sm font-medium text-gray-800">{{ account.roleDesc }}</p>
              </div>
            </div>
          </div>
          <ButtonLink :href="route('admin.account.edit', account.id)" class="edit-button">
            <PencilSquareIcon class="w-5 h-5" />
            {{ t('common.btn.edit') }}
          </ButtonLink>
        </div>
      </div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div>
            <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
              {{ t('models/account.accountStatus') }}
            </h4>
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
              <v-select
                class="w-50"
                :options="approvalStatusOptions"
                v-model="formApproval.approval_status"
                :disabled="account.roleDiv === 'SUPER_ADMIN'"
              />
            </div>
          </div>
          <button class="edit-button" @click="updateStatus()">
            {{ t('common.btn.save') }}
          </button>
        </div>
      </div>
      <div class="flex justify-end">
        <ButtonLink size="sm" variant="outline" :href="route('admin.account.index')">{{
          t('common.btn.back')
        }}</ButtonLink>
      </div>
    </div>
  </div>
</template>
