<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { PencilSquareIcon, TrashIcon } from '@heroicons/vue/24/solid';
import VPagination from '@/components/common/shared/VPagination.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import Button from '@/components/common/shared/Button.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import { AccountType, ListAccountProps } from '@/types/account.ts';
import { ref } from 'vue';
import ConfirmModal from '@/components/common/shared/ConfirmModal.vue';

defineProps<ListAccountProps>();

const { t } = useI18n();
const roleSearchOptions = (window as any).accountRoleSearch;
const searchForm = useForm({
  name: '',
  login_id: '',
  role_div: '',
});

// Modal state
const isModalDeleteAccount = ref(false);
const selectedAccount = ref<AccountType | null>(null);

function submitSearch() {
  searchForm.get(route('admin.account.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.account.index'));
}

function openModalDeleteAccount(account: AccountType) {
  selectedAccount.value = account;
  isModalDeleteAccount.value = true;
}
function deleteAccount() {
  if (confirm('Are you sure you want to delete this account?')) {
    router.delete(route('admin.account.delete', selectedAccount.value?.id));
  }
}
</script>

<template>
  <Head :title="t('models/account.title')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white/90">
      {{ t('models/account.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <h4 class="text-lg font-semibold text-gray-800 lg:mb-6">
            {{ t('common.field.search') }}
          </h4>
          <form @submit.prevent="submitSearch">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-x-6">
              <v-input :label="t('models/account.field.name')" v-model="searchForm.name" class="w-full" />
              <v-input :label="t('models/account.field.login_id')" v-model="searchForm.login_id" class="w-full" />
              <v-select label="Role" v-model="searchForm.role_div" :options="roleSearchOptions" class="w-full" />
            </div>
            <div class="flex items-center justify-center gap-2 mt-6">
              <Button size="sm" variant="outline" type="button" @click="resetSearch">{{
                t('common.btn.reset')
              }}</Button>
              <Button size="sm" variant="primary" type="submit">{{ t('common.btn.search') }}</Button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex items-center justify-end mb-6">
        <button-link size="sm" variant="primary" :href="route('admin.account.create')">{{
          t('common.btn.create')
        }}</button-link>
      </div>
      <div class="overflow-hidden rounded-xl border border-gray-200 bg-white">
        <div class="max-w-full">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/account.field.id') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/account.field.name') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/account.field.login_id') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/account.field.email') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/account.field.role_div') }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase"
                >
                  {{ t('common.field.action') }}
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="account in accounts.data" :key="account.id" class="border-t border-gray-100">
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ account.id }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ account.name }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ account.loginId }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ account.email }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ account.roleDesc }}</span>
                </td>
                <td class="px-5 py-4 sm:px-6">
                  <div class="flex justify-end" v-show="author.id !== account.id">
                    <Link :href="route('admin.account.edit', account.id)"
                      ><pencil-square-icon class="size-5 mr-2"
                    /></Link>
                    <trash-icon
                      class="size-5 mr-2 text-red-600 cursor-pointer"
                      @click="openModalDeleteAccount(account)"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
            <tr v-if="accounts.data.length === 0">
              <td class="px-6 py-4 border-t" colspan="5">{{ t('common.noResult') }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="mt-2" v-if="accounts.data.length > 0">
        <v-pagination :paginator="accounts.paginator" />
      </div>
    </div>
  </div>

  <!-- Delete Modal -->
  <ConfirmModal
    v-model="isModalDeleteAccount"
    :title="'Delete admin'"
    size="md"
    @close="isModalDeleteAccount = false"
    :saveButtonText="t('common.btn.delete')"
    saveButtonVariant="danger"
    @save="deleteAccount()"
  >
    <div class="grid grid-cols-1 gap-x-6 gap-y-5 pr-2">
      <div class="border-b pb-4">
        <div class="grid grid-cols-1 gap-4">
          <div>
            <p class="text-sm text-gray-500">{{ t('models/account.field.name') }}</p>
            <p class="font-medium">{{ selectedAccount?.name }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-500">{{ t('models/account.field.email') }}</p>
            <p class="font-medium">{{ selectedAccount?.email }}</p>
          </div>
          <div>
            <p class="text-sm text-gray-500">{{ t('models/account.field.role_div') }}</p>
            <p class="font-medium">{{ selectedAccount?.roleDesc }}</p>
          </div>
        </div>
      </div>
    </div>
  </ConfirmModal>
</template>
