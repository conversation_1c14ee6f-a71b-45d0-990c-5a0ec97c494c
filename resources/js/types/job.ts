import { Paginator } from '@/types/paginator.ts';
import { GroupType } from '@/types/group.ts';

export type JobFormType = {
  group_id: number;
  is_recommended: boolean;
  recruitment_type: string;
  employer_email: string;
  employer_name: string;
  employer_phone_number: string;
  category_id: number;
  type: string;
  is_public: boolean;
  is_instant: boolean;
  thumbnail: File | null;
  images: File[];
  title: string;
  description: string;
  benefits?: string;
  time_start?: string;
  time_end?: string;
  age?: number;
  gender?: string;
  quantity?: number;
  certificate_level?: string;
  prefecture: string;
  address: string;
  salary_type: string;
  salary?: number;
  travel_fee_type: string;
  travel_fee?: number;
  recruit_start_at: string | null;
  recruit_expired_at: string | null;
  job_start_at: string | null;
  // Image management fields for edit mode
  deleted_image_ids?: number[];
  existing_images?: Images[];
};

export type ListJobType = {
  id: number;
  type: string;
  typeName: string;
  title: string;
  timeStart?: string;
  timeEnd?: string;
  quantity?: number;
  prefecture: string;
  countApplied?: number;
  isFilled: boolean;
};

export type JobType = ListJobType & {
  groupId: number;
  groupName: string;
  isRecommended: boolean;
  recruitmentType: string;
  employerEmail: string;
  employerName: string;
  employerPhoneNumber: string;
  categoryId: number;
  categoryName: string;
  isPublic: boolean;
  isInstant: boolean;
  thumbnailId: number;
  thumbnailUrl: string;
  description: string;
  benefits?: string;
  age?: number;
  gender?: string;
  certificateLevel: string;
  address: string;
  salaryType: string;
  salary?: number;
  travelFeeType: string;
  travelFee?: number;
  recruitStartAt: string;
  recruitExpiredAt: string;
  jobStartAt: string;
  createdAt: string;
  updatedAt: string;
  images: Images[];
};

type Images = {
  id: number;
  imageUrl: string;
};

export type JobCategoryType = {
  id: number;
  name: string;
};

export type ListJobProps = {
  jobs: {
    data: ListJobType[];
    paginator: Paginator;
  };
};

export type DetailJobProps = {
  job: JobType;
};

export type CreateJobProps = {
  categories: JobCategoryType[];
  groups: GroupType[];
};

export type EditJobProps = {
  job: JobType;
  categories: JobCategoryType[];
  groups: GroupType[];
};
