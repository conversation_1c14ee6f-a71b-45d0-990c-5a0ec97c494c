import { Paginator } from "@/types/paginator.ts";

export type ListNotificationProps = {
    author: object;
    notifications: {
        data: NotificationType[];
        paginator: Paginator
    };
}

export type DetailNotificationProps = {
    notification: NotificationType;
}

export type NotificationType = {
    id: number;
    title: string;
    body: string;
    type: string;
    jobId: string;
    startNotificationAt: string;
    endNotificationAt: string;
}

export type NotificationFormType = {
    type: string;
    title: string;
    body: string;
    job_id: string;
    start_notification_at: string;
    end_notification_at: string;
}
