import { Paginator } from '@/types/paginator.ts';

export type ListPolicyProps = {
  policies: {
    data: PolicyType[];
    paginator: Paginator;
  };
};

export type DetailPolicyProps = {
  policy: PolicyType;
};

export type PolicyType = {
  id: number;
  title: string;
  body: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
};

export type PolicyFormType = {
  title: string;
  body: string;
  is_public: boolean;
};
