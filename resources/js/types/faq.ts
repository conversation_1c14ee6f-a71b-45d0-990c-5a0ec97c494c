import { Paginator } from "@/types/paginator.ts";

export type ListFaqProps = {
    faqs: {
        data: FaqType[];
        paginator: Paginator
    };
}

export type DetailFaqProps = {
    faq: FaqType;
}

export type FaqType = {
    id: number;
    question: string;
    answer: string;
    isPublic: boolean;
    createdAt: string;
    updatedAt: string;
}

export type FaqFormType = {
    question: string;
    answer: string;
    is_public: boolean;
}
