import { Paginator } from '@/types/paginator.ts';

export type ListAccountProps = {
  author: AccountType;
  accounts: {
    data: AccountType[];
    paginator: Paginator;
  };
};

export type DetailAccountProps = {
  account: AccountType;
};

export type DetailAuthorType = {
  author: AccountType;
};

export type AccountType = {
  id: number;
  loginId: string;
  name: string;
  email: string;
  roleDiv: string;
  roleDesc: string;
  approvalStatus: string;
  approvalStatusName: string;
  avatarUrl?: string;
};

export type AccountEditFormType = {
  login_id?: string;
  name: string;
  email: string;
  password?: string;
  password_confirmation?: string;
  role_div: string;
};
