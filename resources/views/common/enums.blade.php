<script>
  window.genderOptions = {!! json_encode(\Src\Enums\Gender::toSelectArray()) !!};
  window.genderOptionTextAll = {!! json_encode(\Src\Enums\Gender::toSelectArrayTextAll()) !!};
  window.bankTypeOptions = {!! json_encode(\Src\Enums\UserBankType::toSelectArray()) !!};
  window.accountRole = {!! json_encode(\Src\Enums\AccountRole::toSelectArray()) !!};
  window.accountRoleSearch = {!! json_encode(\Src\Enums\AccountRole::toSelectArrayTextAll()) !!};
  window.countriesOptions = {!! json_encode(list_countries()) !!};
  window.approvalStatusOptions = {!! json_encode(\Src\Enums\ApprovalStatus::toSelectArray()) !!};
  window.approvalSearchOptions = {!! json_encode(\Src\Enums\ApprovalStatus::toSelectArrayTextAll()) !!};
  window.booleanOptions = {!! json_encode(\Src\Enums\Boolean::toSelectArray()) !!};
  window.standardCertificateLevels = {!! json_encode(\Src\Enums\CertificateLevel::getStandardLevels()) !!};
  window.sameCertificateLevels = {!! json_encode(\Src\Enums\CertificateLevel::getSameLevels()) !!};
  window.notificationType = {!! json_encode(\Src\Enums\NotificationType::toSelectArray()) !!};
  window.recruitmentTypeOptions = {!! json_encode(\Src\Enums\RecruitmentType::toSelectArray()) !!};
  window.jobTypeOptions = {!! json_encode(\Src\Enums\JobType::toSelectArray()) !!};
  window.feeTypeOptions = {!! json_encode(\Src\Enums\FeeType::toSelectArray()) !!};
  window.userStatusOptions = {!! json_encode(\Src\Enums\UserStatus::toSelectArray()) !!};
  window.residenceCardStatusOptions = {!! json_encode(\Src\Enums\ResidenceCardStatus::toSelectArray()) !!};
  window.prefectureOptions = {!! json_encode(list_prefectures()) !!}
</script>
