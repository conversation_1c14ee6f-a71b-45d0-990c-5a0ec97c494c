{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "skipLibCheck": true,
        "allowJs": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "preserve",

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,

        /* Path aliases */
        "baseUrl": ".",
        "paths": {
            "@/*": ["resources/js/*"]
        },

        "types": ["vite/client", "ziggy-js"]
    },
    "files": [],
    "include": [
        "resources/**/*.ts",
        "resources/**/*.d.ts",
        "resources/**/*.vue"
    ],
    "references": [
        {
            "path": "./tsconfig.node.json"
        },
    ]
}
