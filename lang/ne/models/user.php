<?php

return [
    'title' => 'User',
    'screenName' => [
        'index' => 'User',
        'create' => 'Create User',
        'edit' => 'Edit User',
        'detail' => 'User Detail',
    ],
    'cardTitle' => [
        'baseInfo' => 'Base Information',
        'address' => 'Address',
        'residenceCard' => 'Residence Card',
        'emergencyBank' => 'Emergency Contact And Bank Information',
        'residencePassport' => 'Residence Card & Passport',
        'bankInfo' => 'Bank Information',
    ],
    'field' => [
        'id' => 'ID',
        'code' => 'Code',
        'name' => 'Name',
        'email' => 'Email',
        'password' => 'Password',
        'confirm_password' => 'Confirm Password',
        'name_kana' => 'Name Kana',
        'name_kanji' => 'Name Kanji',
        'avatar' => 'Avatar',
        'health_certificate' => 'Health Certificate',
        'phone_number' => 'Phone Number',
        'gender' => 'Gender',
        'birthday' => 'Birthday',
        'nationality' => 'Nationality',
        'japanese_level' => 'Japanese Level',
        'arrival_date' => 'Arrival Date',
        'zipcode' => 'Zipcode',
        'prefecture' => 'Prefecture',
        'street_address' => 'Street Address',
        'town_address' => 'Town Address',
        'user_status' => 'Status',
        'train_station_name' => 'Train Station',
        'emergency_name' => 'Emergency Name',
        'emergency_relation' => 'Emergency Relation',
        'emergency_phone' => 'Emergency Phone',
        'bank_type' => 'Bank Type',
        'email_verification_at' => 'Email Verification Date',
        'identification_at' => 'Identification Date',
        'is_disable' => 'Disable',
        'point' => 'Point',
        'passport_image' => 'Passport Image',
        'passport_number' => 'Passport Number',
        'passport_expired_at' => 'Passport Expired At',
        'job_participation_count' => 'Job Participation Count',
    ],
    'search_field' => [
        'name_email_phone' => 'Name/Email/Phone',
        'prefecture' => 'Prefecture',
        'status' => 'Status Account',
        'job_participation_count' => 'Job Participation Count',
        'status_residence_card' => 'Status Residence Card',
        'gender' => 'Gender',
        'age' => 'Age',
    ]
];
