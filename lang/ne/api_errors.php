<?php

return [
    'auth' => [
        'login_failed' => [
            'message' => 'इमेल वा पासवर्ड गलत छ!',
            'code' => '500',
        ],
        'not_verify_email' => [
            'message' => 'Please verify your email address.',
            'code' => '403',
        ],
        'not_approved' => [
            'message' => 'Please wait for approval admin.',
            'code' => '403',
        ],
    ],
    'prefecture' => [
        'not_found' => [
            'message' => 'Zipcode not found.',
            'code' => '404',
        ],
        'required' => [
            'message' => 'Zipcode is required.',
            'code' => '400',
        ],
    ],
    'user' => [
        'not_found' => [
            'message' => 'User not found.',
            'code' => '404',
        ],
        'reset_password_exist' => [
            'message' => 'Link reset password has sent, please check your email.',
            'code' => '302'
        ],
        'old_password_not_match' => [
            'message' => 'Old password not match.',
            'code' => '403'
        ],
        'token_not_found' => [
            'message' => 'Token not found.',
            'code' => '404'
        ],
        'token_expired' => [
            'message' => 'Token expired.',
            'code' => '410'
        ],
        'birthday_not_match' =>[
            'message' => 'The entered information is invalid.',
            'code' => '403'
        ],
        'link_does_not_exist' => [
            'message' => 'link does not exist',
            'code' => '400'
        ],
        'locked' => [
            'message' => 'This user is locked due to too many job application cancellations.',
            'code' => '400'
        ],
        'requires_admin_approval' => [
            'message' => 'Requires Admin Approval.',
            'code' => '403',
        ],
        'reset_password_cooldown' => [
            'message' => 'You must wait before requesting another password reset.',
            'code' => '429',
        ],
    ],
    'job' => [
        'not_found' => [
            'message' => 'Job not found.',
            'code' => '404',
        ],
        'unable_to_retrieve_job_information' => [
            'message' => 'Unable to retrieve job information',
            'code' => '500'
        ],
        'job_have_not_apply' => [
            'message' => "You haven't applied for this job.",
            'code' => '400'
        ],
        'cancel_job_fails' => [
            'message' => "Failed to cancel this job",
            'code' => '400'
        ],
        'user_has_already_applied_for_this_job' => [
            'message' => 'User has already applied for this job.',
            'code' => '409',
        ],
        'user_failed_to_apply_for_this_job' => [
            'message' => 'User failed to apply for this job.',
            'code' => '500',
        ],
        'job_not_found' => [
            'message' => 'Job not found.',
            'code' => '404',
        ],
        'apply_failed' => [
            'message' => 'Failed to apply for the job.',
            'code' => '400',
        ],
        'user_has_already_favorite_this_job' => [
            'message' => 'User has already favorite this job.',
            'code' => '409',
        ],
        'favorite_failed' => [
            'message' => 'Failed to favorite the job.',
            'code' => '400',
        ],
        'unauthenticated' => [
            'message' => 'Unauthenticated.',
            'code' => '500',
        ],
        'favorite_deletion_failed' => [
            'message' => 'Favorite Deletion Failed',
            'code' => '400',
        ],
        'user_has_not_already_favorite_this_job' => [
            'message' => 'The user has not favorited this job yet.',
            'code' => '400',
        ],
        'the_number_of_applicants_is_full' => [
            'message' => 'The number of applicants is full.',
            'code' => '409',
        ],
        'is_disabled' => [
            'message' => 'The account is locked until :disable_until_at',
            'code' => '400',
        ],
        'is_recommended' => [
            'message' => 'The account is not allowed to apply for jobs',
            'code' => '400',
        ],
    ],
    'job_category' => [
        'not_found' => [
            'message' => 'Job category not found.',
            'code' => '404',
        ],
    ],
    'notification' => [
        'not_found' => [
            'message' => 'Notification not found.',
            'code' => '404',
        ],
    ],
    'residence_card' => [
        'requires_admin_approval' => [
            'message' => 'Requires Admin Approval.',
            'code' => '403',
        ],
        'other_type_not_allowed' => [
            'message' => 'Other Type Not Allowed.',
            'code' => '403',
        ],
        'expired' => [
            'message' => 'Residence card has expired.',
            'code' => '403',
        ],
    ],
    'faq' => [
        'failed_to_fetch_faq' => [
            'message' => 'Failed to fetch FAQs.',
            'code' => '403',
        ],
    ],
    'policy' => [
        'failed_to_fetch_policy' => [
            'message' => 'Failed to fetch Policy.',
            'code' => '403',
        ],
    ],
];
