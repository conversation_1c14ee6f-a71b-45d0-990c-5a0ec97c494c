<?php

return [
    'title' => '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'screenName' => [
        'index' => '<PERSON><PERSON><PERSON>',
        'create' => '<PERSON>uat <PERSON>ek<PERSON> Baru',
        'edit' => '<PERSON>bah Pekerjaan',
        'detail' => 'Detail Peker<PERSON>an'
    ],
    'field' => [
        'id' => 'No',
        'recruitment_type' => 'Jenis Rekrutmen',
        'employer_email' => 'Email Pemberi Kerja',
        'employer_name' => 'Nama Pemberi Kerja',
        'employer_phone_number' => 'Nomor Telepon Pemberi Kerja',
        'category' => 'Kategori',
        'type' => 'Tipe',
        'is_public' => 'Publik',
        'is_instant' => 'Instan',
        'thumbnail' => 'Thumbnail',
        'images' => 'Gambar Pekerjaan',
        'title' => 'Judul',
        'description' => 'Deskripsi',
        'benefits' => 'Manfaat',
        'time_start' => 'Waktu <PERSON>',
        'time_end' => '<PERSON><PERSON><PERSON>',
        'age' => '<PERSON>ia',
        'gender' => '<PERSON><PERSON>',
        'quantity' => 'Jumlah',
        'certificate_level' => 'Tingkat Sertifikat',
        'prefecture' => 'Prefektur',
        'address' => 'Alamat',
        'salary_type' => 'Jenis Gaji',
        'salary' => 'Gaji',
        'travel_fee_type' => 'Jenis Biaya Perjalanan',
        'travel_fee' => 'Biaya Perjalanan',
        'recruit_start_at' => 'Rekrutmen Mulai Pada',
        'recruit_expired_at' => 'Rekrutmen Berakhir Pada',
        'job_start_at' => 'Pekerjaan Mulai Pada',
        'is_filled' => 'Status Keterisian',
        'job_time' => 'Waktu Kerja'
    ],
    'searchField' => [
        'time' => 'Waktu Kerja'
    ],
    'is_filled' => [
        'ended' => 'Sudah Terisi',
        'not_ended' => 'Belum Terisi',
    ],
];
