<?php

use Src\Enums\AccountRole;
use Src\Enums\ApprovalStatus;
use Src\Enums\Boolean;
use Src\Enums\CertificateLevel;
use Src\Enums\DepositType;
use Src\Enums\EmergencyRelation;
use Src\Enums\FAQType;
use Src\Enums\FeeType;
use Src\Enums\Gender;
use Src\Enums\JobType;
use Src\Enums\NotificationType;
use Src\Enums\PeriodType;
use Src\Enums\PolicyType;
use Src\Enums\RecruitmentType;
use Src\Enums\ResidenceCardStatus;
use Src\Enums\UserBankType;
use Src\Enums\UserStatus;

return [
    AccountRole::class => [
        AccountRole::SUPER => 'スーパー管理者',
        AccountRole::NORMAL => '管理者',
        AccountRole::READ => '読み取り専用',
    ],
    ApprovalStatus::class => [
        ApprovalStatus::WAITING => '待機中',
        ApprovalStatus::APPROVED => '承認済み',
        ApprovalStatus::DECLINED => '却下',
    ],
    Boolean::class => [
        Boolean::YES => 'あり',
        Boolean::NO => 'なし',
    ],
    CertificateLevel::class => [
        CertificateLevel::N1 => 'N1',
        CertificateLevel::N2 => 'N2',
        CertificateLevel::N3 => 'N3',
        CertificateLevel::N4 => 'N4',
        CertificateLevel::N5 => 'N5',
        CertificateLevel::SAME_N1 => 'N1相当',
        CertificateLevel::SAME_N2 => 'N2相当',
        CertificateLevel::SAME_N3 => 'N3相当',
        CertificateLevel::SAME_N4 => 'N4相当',
        CertificateLevel::SAME_N5 => 'N5相当',
    ],
    DepositType::class => [
        DepositType::NORMAL => '普通',
        DepositType::CURRENT => '当座',
        DepositType::FIXED => '定期',
    ],
    EmergencyRelation::class => [
        EmergencyRelation::FAMILY => '家族',
        EmergencyRelation::RELATIVE => '親戚',
        EmergencyRelation::FRIEND => '友人',
        EmergencyRelation::SCHOOL => '学校関係者',
    ],
    FeeType::class => [
        FeeType::DAY => '日単位',
        FeeType::HOUR => '時間単位'
    ],
    Gender::class => [
        Gender::MALE => '男性',
        Gender::FEMALE => '女性',
    ],
    JobType::class => [
        JobType::REGULAR => 'レギュラー',
        JobType::SPOT => 'スポット',
    ],
    NotificationType::class => [
        NotificationType::PUBLIC => '公開',
        NotificationType::SYSTEM => 'システム',
        NotificationType::JOB => '仕事'
    ],
    RecruitmentType::class => [
        RecruitmentType::ADMIN => '管理者',
        RecruitmentType::AUTO => '自動'
    ],
    UserBankType::class => [
        UserBankType::CASH => '現金',
        UserBankType::BANK => '銀行',
    ],
    UserStatus::class => [
        UserStatus::INITIAL => '登録済',
        UserStatus::VERIFIED => '認証済',
        UserStatus::APPROVED => '承認済',
        UserStatus::DISABLED => '無効化'
    ],
    ResidenceCardStatus::class => [
        ResidenceCardStatus::APPROVED => '承認済',
        ResidenceCardStatus::WAITING => '承認待ち',
        ResidenceCardStatus::DECLINED => '無効化',
        ResidenceCardStatus::RENEW => '更新申請中',
    ],
    PeriodType::class => [
        PeriodType::STUDENT => '留学',
        PeriodType::FAMILY => '家族滞在',
        PeriodType::SPECIFIC_ACTIVITY => '特定活動',
        PeriodType::PERMANENT_RESIDENT => '永住者',
        PeriodType::SPOUSE_OF_JAPANESE => '日本人の配偶者等',
        PeriodType::SPOUSE_OF_PERMANENT_RESIDENT => '永住者の配偶者等',
        PeriodType::LONG_TERM_RESIDENT => '定住者',
        PeriodType::OTHER => 'その他',
    ],
    FAQType::class => [
        FAQType::HOW_TO_USE => 'アプリの使い方',
        FAQType::REGISTRATION => '登録・退会',
        FAQType::SEARCH => '探す',
        FAQType::APPLY => '申し込む',
        FAQType::CANCEL_TROUBLE => 'キャンセル・トラブル',
        FAQType::REVIEW_PENALTY => '評価・ペナルティ',
        FAQType::PAYMENT => '給与受け取り',
        FAQType::BUG_CONTACT => '不具合・お問い合わせ',
        FAQType::ANNOUNCEMENT => 'お知らせ',
    ],
    PolicyType::class => [
        PolicyType::TERMS => '利用規約',
        PolicyType::PRIVACY => 'プライバシーポリシー',
    ],
];
