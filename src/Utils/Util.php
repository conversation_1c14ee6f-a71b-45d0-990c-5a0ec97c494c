<?php

namespace Src\Utils;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;

class Util
{
    /**
     * Add search condition to query
     *
     * @param Builder $query
     * @param array $conditions - array search conditions, format as: [ [{key}, {operator}, {value}] ]
     * @return Builder
     */
    public static function addSearchCondition(Builder $query, array $conditions): Builder
    {
        if (empty($conditions)) {
            return $query;
        }
        foreach ($conditions as $condition) {
            if (count($condition) !== 3) {
                continue;
            }
            [$key, $operator, $value] = $condition;
            if ($value !== null) {
                if (Str::lower($operator) === 'in') {
                    $query = $query->whereIn($key, $value);
                    continue;
                }
                if (strcasecmp($operator, 'like') === 0) {
                    $query = $query->where($key, $operator, '%' . $value . '%');
                } elseif (strcasecmp($operator, 'between') === 0 && is_array($value)) {
                    $start = reset($value);
                    $end = end($value);
                    $query = $query->where($key, '>=', $start)
                        ->where($key, '<=', $end);
                } else {
                    $query = $query->where($key, $operator, $value);
                }
            }
        }
        return $query;
    }
}
