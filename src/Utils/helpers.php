<?php

use App\Exceptions\APIRuntimeException;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Src\Enums\ResultCode;
use Illuminate\Support\Str;

if (!function_exists('json_response')) {
    /**
     * Json response api
     *
     * @param $result_code
     * @param null $result_detail
     * @param null $result_error
     * @return \Illuminate\Http\JsonResponse
     */
    function json_response($result_code, $result_detail = null, $result_error = null)
    {
        $obj_to_json = [
            'result_code' => $result_code,
            'result_detail' => $result_detail,
        ];

        if (ResultCode::isError($result_code) && $result_error) {
            if (is_string($result_error)) {
                $response_error['code'] = 500;
                $response_error['message'] = $result_error;
            } else {
                $response_error = $result_error;
            }

            $obj_to_json['result_error'] = $response_error;
        }

        return response()->json($obj_to_json);
    }
}

if (!function_exists('array_get_int')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|int
     */
    function array_get_int(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (int)$value;
    }
}
if (!function_exists('array_get_float')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|float
     */
    function array_get_float(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (float)$value;
    }
}
if (!function_exists('array_get_string')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|string
     */
    function array_get_string(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (string)$value;
    }
}
if (!function_exists('array_get_bool')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|bool
     */
    function array_get_bool(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (bool)$value;
    }
}
if (!function_exists('array_get_array')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|array
     */
    function array_get_array(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (array)$value;
    }
}
if (!function_exists('array_get_carbon')) {
    /**
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|\Carbon\Carbon
     */
    function array_get_carbon(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        $result = null;
        if ($value instanceof \Carbon\Carbon) {
            $result = $value;
        } else if (is_string($value)) {
            $result = \Carbon\Carbon::parse($value);
        }
        return $result;
    }
}

if (!function_exists('str_random')) {
    /**
     * Generate a more truly "random" alpha-numeric string.
     *
     * @param int $length
     * @return string
     *
     * @throws \RuntimeException
     */
    function str_random($length = 16)
    {
        return Str::random($length);
    }
}

if (!function_exists('throw_if_api_exception')) {

    /**
     * throw_if_api_exception
     *
     * @param Throwable $e
     * @throws Throwable
     */
    function throw_if_api_exception(Throwable $e)
    {
        if($e instanceof APIRuntimeException){
            throw $e;
        }

        if($e instanceof ModelNotFoundException){
            throw $e;
        }
    }
}

if (!function_exists('random_number')) {
    function random_number($length = 16) {
        $result = '';
        for ($i = 0; $i < $length; $i++) {
            $result .= mt_rand(0, 9);
        }
        return $result;
    }
}

if (!function_exists('gen_code')) {
    function gen_code(array $exist_code, $length = 6) {
        do {
            $min = (int) ('2' . str_repeat('0', $length - 1));
            $max = (int) str_repeat('9', $length);
            $new_code = mt_rand($min, $max);
        } while (in_array($new_code, $exist_code));

        return $new_code;
    }
}

if (!function_exists('pagination')) {
    function pagination($paginator, array $options = []): array
    {
        $paginator_data = $paginator->toArray();
        $result = [];
        $result['data'] = $paginator_data['data'] ?? [];
        $result['paginator'] = [
            'currentPage' => $paginator_data['current_page'],
            'firstPageUrl' => $paginator_data['first_page_url'],
            'from' => $paginator_data['from'],
            'lastPage' => $paginator_data['last_page'],
            'lastPageUrl' => $paginator_data['last_page_url'],
            'nextPageUrl' => $paginator_data['next_page_url'],
            'path' => $paginator_data['path'],
            'perPage' => $paginator_data['per_page'],
            'prevPageUrl' => $paginator_data['prev_page_url'],
            'to' => $paginator_data['to'],
            'total' => $paginator_data['total']
        ];

        if (!empty($options)) {
            $result = array_merge($result, $options);
        }

        return $result;
    }
}

if (!function_exists('presigned_url')) {
    function presigned_url(string $path): string
    {
        $expiry_time = Carbon::now()->addMinutes(30);

        return Storage::disk('s3')->temporaryUrl($path, $expiry_time);
    }
}

if (!function_exists('list_countries')) {
    function list_countries(): array
    {
        $jsonPath = storage_path('app/private/countries.json');
        if (!file_exists($jsonPath)) {
            return [];
        }

        $jsonContent = file_get_contents($jsonPath);
        $countries = json_decode($jsonContent, true);

        $result = [];
        foreach ($countries as $country) {
            $result[$country['code']] = $country['country'];
        }

        asort($result);
        return $result;
    }
}

if (!function_exists('country_name')) {
    function country_name($code)
    {
        $countries = list_countries();
        return $countries[$code] ?? null;
    }
}

if (!function_exists('logger_info')) {
    /**
     * Log an info message with optional context.
     *
     * @param string $message
     * @param array $context Additional data for context.
     * @return void
     */
    function logger_info(string $message, array $context = []): void
    {
        Log::info($message, $context);
    }
}

if (!function_exists('logger_error')) {
    /**
     * Log an error message with optional context and exception details.
     *
     * Automatically adds exception file and line to context if provided.
     *
     * @param string $message
     * @param array $context Additional data for context.
     * @param Throwable|null $e The exception object.
     * @return void
     */
    function logger_error(string $message, array $context = [], Throwable $e = null): void
    {
        // If an exception is provided, add its details to the context
        if ($e !== null) {
            $context['exception'] = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ];
        }

        // Log the error with the message and updated context
        Log::error($message, $context);
    }
}

if (!function_exists('list_prefectures')) {
    function list_prefectures(): array
    {
        $prefectures = DB::table('m_prefectures')->select('prefecture')->distinct()->get();

        return $prefectures->map(function ($prefecture) {
            return [
                'value' => $prefecture->prefecture,
                'label' => $prefecture->prefecture,
            ];
        })->toArray();
    }
}
