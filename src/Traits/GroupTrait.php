<?php

namespace Src\Traits;

use App\Eloquent\Group;

trait GroupTrait
{
    /**
     * @return array
     */
    public function fetchGroups(): array
    {
        return Group::queryModel()->get()->map(function (Group $group) {
            return [
                'id' => $group->id,
                'wssCode' => $group->wss_code,
                'companyName' => $group->company_name,
                'siteName' => $group->site_name,
            ];
        })->toArray();
    }
}
