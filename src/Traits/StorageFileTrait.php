<?php

namespace Src\Traits;

use App\Eloquent\StorageFile;
use App\Exceptions\ErrorException;
use Illuminate\Http\UploadedFile;
use Src\Traits\Utils\FileUpload\FileUploadable;

trait StorageFileTrait
{
    use FileUploadable;

    /**
     * Put and save file to storageFile
     *
     * @param UploadedFile $file
     * @param int $fileDiv
     * @param string $path
     * @return int
     * @throws ErrorException
     */
    public function createStorageFile(UploadedFile $file, int $fileDiv, string $path = 'uploads'): int
    {
        $fileInfo = $this->putFileToStorage($file, $path);

        /** @var StorageFile $storageFile */
        $storageFile = $this->storageFileQuery()->createOrThrow([
            'file_div' => $fileDiv,
            'file_path' => $fileInfo['file_path'],
            'file_type' => $fileInfo['file_type'],
            'file_size' => $fileInfo['file_size'],
            'file_url' => $fileInfo['file_url']
        ]);

        return $storageFile->id;
    }

    /**
     * @return StorageFile
     */
    private function storageFileQuery(): StorageFile
    {
        return StorageFile::queryModel();
    }
}
