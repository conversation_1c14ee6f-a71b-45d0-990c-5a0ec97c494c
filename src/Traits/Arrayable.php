<?php

namespace Src\Traits;

use Carbon\Carbon;

/**
 * Trait Arrayable
 */
trait Arrayable
{
    /**
     * @return array
     */
    public function toArray(): array
    {
        $result = [];
        foreach (get_class_vars(get_class($this)) as $key => $val) {
            if ($key === 'fields') {
                continue;
            }
            if (is_array($this->{$key})) {
                foreach ($this->{$key} as $i => $v) {
                    $result[$key][$i] = method_exists($v, 'toArray') ? $v->toArray() : $v;
                }
            } else if ($this->{$key} instanceof Carbon) {
                $result[$key] = $this->{$key};
            } else {
                $result[$key] = $this->{$key};
            }
        }
        return $result;
    }

    /**
     * @param array $values
     * @return array
     */
    private function snakeToCamelCaseFormArray(array $values): array
    {
        $result = [];
        foreach ($values as $key => $value) {
            // 再帰の実装が必要になったらする
            $key = $this->snakeToCamelCase($key);
            $result[$key] = $value;
        }
        return $result;
    }

    private function snakeToCamelCase($string, $capitalizeFirstCharacter = false)
    {

        $str = str_replace('_', '', ucwords($string, '_'));

        if (!$capitalizeFirstCharacter) {
            $str = lcfirst($str);
        }

        return $str;
    }

}
