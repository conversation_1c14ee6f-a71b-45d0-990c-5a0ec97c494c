<?php

namespace Src\Traits;

use App\Exceptions\ErrorException;
use Src\Enums\FileDiv;

trait UserTrait
{
    use StorageFileTrait;

    /**
     * @param array $attributes
     * @param array $fileFields
     * @param array $uploadedFiles
     * @return array
     * @throws ErrorException
     */
    public function prepareUserAttributes(array $attributes, array $fileFields, array &$uploadedFiles): array
    {
        foreach ($fileFields as $field => $file) {
            if ($file) {
                $fileId = $this->createStorageFile($file, FileDiv::IMAGE,$attributes["code"]."/info");
                if ($fileId) {
                    $attributes[$field] = $fileId;
                    $uploadedFiles[] = $fileId;
                }
            }
        }
        return $attributes;
    }

    /**
     * @param array $attributes
     * @param array $fileFields
     * @return array
     * @throws ErrorException
     */
    public function prepareResidenceCardAttributes(array $attributes,string $code, array $fileFields): array
    {
        foreach ($fileFields as $field => $file) {
            if ($file) {
                $fileId = $this->createStorageFile($file, FileDiv::IMAGE, "$code/residence_card");
                if ($fileId) {
                    $attributes[$field] = $fileId;
                }
            }
        }

        return $attributes;
    }
}
