<?php

namespace Src\Traits;

use Src\Domain\Admin\Services\EditorService;

trait EditorImageTrait
{
    /**
     * Process editor content to move temporary images to permanent storage
     *
     * @param string $content
     * @return string
     */
    protected function processEditorImages(string $content): string
    {
        $editorService = new EditorService();
        return $editorService->moveTemporaryImagesToPermanent($content);
    }

    /**
     * Process multiple editor content fields
     * 
     * @param array $fields
     * @param array $data
     * @return array
     */
    protected function processMultipleEditorFields(array $fields, array $data): array
    {
        foreach ($fields as $field) {
            if (isset($data[$field]) && is_string($data[$field])) {
                $data[$field] = $this->processEditorImages($data[$field]);
            }
        }

        return $data;
    }
}
