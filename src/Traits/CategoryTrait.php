<?php

namespace Src\Traits;

use App\Eloquent\JobCategory;

trait CategoryTrait
{
    /**
     * @return array
     */
    public function fetchCategories(): array
    {
        return JobCategory::query()->orderBy('name')->get()->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
            ];
        })->toArray();
    }
}
