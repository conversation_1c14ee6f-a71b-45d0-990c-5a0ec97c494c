<?php

namespace Src\Enums;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

class BaseEnum extends Enum implements LocalizedEnum
{
    /**
     * Get array key value for select control in search form
     *
     * @return array
     */
    public static function toSelectArrayTextAll(): array
    {
        $list = ['' => __('common.field.all')];
        return array_replace($list, self::asSelectArray());
    }

    /**
     * Get array key value with not select element for select control in form
     *
     * @return array
     */
    public static function getOptionalSelectArray(): array
    {
        $list = ['' => ''];
        return array_replace($list, self::asSelectArray());
    }

    /**
     * Get array key value for select control in form
     *
     * @return array
     */
    public static function getSelectArray(): array
    {
        return self::asSelectArray();
    }


    /**
     * Get select array from compress value
     *
     * @param int $compress_val
     * @return array
     */
    public static function extractSelectArrayFromCompressValue(int $compress_val): array
    {
        return array_filter(self::getSelectArray(), function ($key) use ($compress_val) {
            return $key & $compress_val;
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * Get selected values from compress value
     *
     * @param int $compress_val
     * @return array
     */
    public static function extractValueArrayFromCompressValue(int $compress_val): array
    {
        $enum_selects = self::extractSelectArrayFromCompressValue($compress_val);
        return array_keys($enum_selects);
    }

    /**
     * Get array of objects with label and value for select controls
     * Format: [{label: description, value: value}, ...]
     *
     * @return array
     */
    public static function toApiResponse(): array
    {
        $result = [];
        $values = self::getValues();

        foreach ($values as $value) {
            $result[] = [
                'label' => static::getDescription($value),
                'value' => $value
            ];
        }

        return $result;
    }
}
