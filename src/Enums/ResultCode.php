<?php

namespace Src\Enums;

/**
 * ResultCode enum.
 *
 * This enum defines the possible result codes for operations.
 */
class ResultCode extends BaseEnum
{
    /**
     * Operation was successful.
     */
    public const SUCCESS = 200;
    /**
     * An error occurred during the operation.
     */
    public const ERROR = 500;
    /**
     * Invalid input was provided.
     */
    public const ERROR_INPUT = 400;
    /**
     * The provided token is invalid.
     */
    public const TOKEN_INVALID = 401;

    /**
     * The requested resource was not found.
     */
    public const NOT_FOUND = 404;

    /**
     * The requested resource was forbidden.
     */
    public const FORBIDDEN = 403;

    public static function isError($result_code): bool
    {
        return $result_code !== self::SUCCESS;
    }
}
