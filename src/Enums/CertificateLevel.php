<?php

namespace Src\Enums;

class CertificateLevel extends BaseEnum
{
    public const N1 = 'N1';
    public const N2 = 'N2';
    public const N3 = 'N3';
    public const N4 = 'N4';
    public const N5 = 'N5';
    public const SAME_N1 = 'SAME_N1';
    public const SAME_N2 = 'SAME_N2';
    public const SAME_N3 = 'SAME_N3';
    public const SAME_N4 = 'SAME_N4';

    public const SAME_N5 = 'SAME_N5';

    /**
     * Get standard levels [N1, N2, N3, N4, N5].
     *
     * @return array
     */
    public static function getStandardLevels(): array
    {
        $standardLevels = [
            self::N1,
            self::N2,
            self::N3,
            self::N4,
            self::N5,
        ];
        
        $selectArray = [];
        foreach ($standardLevels as $value) {
            $selectArray[$value] = static::getDescription($value);
        }
        
        return $selectArray;
    }

    /**
     * Get same levels [SAME_N1, SAME_N2, SAME_N3, SAME_N4, SAME_N5].
     *
     * @return array
     */
    public static function getSameLevels(): array
    {
        $sameLevels = [
            self::SAME_N1,
            self::SAME_N2,
            self::SAME_N3,
            self::SAME_N4,
            self::SAME_N5,
        ];
        
        $selectArray = [];
        foreach ($sameLevels as $value) {
            $selectArray[$value] = static::getDescription($value);
        }
        
        return $selectArray;
    }
}
