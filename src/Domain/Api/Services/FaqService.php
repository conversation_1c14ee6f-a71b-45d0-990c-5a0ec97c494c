<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Faq;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Faq\FAQDetail;

/**
 * Class FaqService
 * @package Src\Domain\Api\Services
 */
class FaqService
{
    /**
     * List FAQ
     *
     *
     * @return array
     */
    public function fetchAll(): array
    {
        $paginator = $this->faqsQuery()->isPublic()->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($faq) {
            return (new FAQDetail($faq))->toDetailApiResponse();
       });

        return pagination($paginator);
    }

    /**
     * Get FAQ Detail
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        $faq = $this->faqsQuery()->isPublic()->findOrFail($id);

        return (new FAQDetail($faq))->toDetailApiResponse();

    }

    /**
     *
     * @return Builder
     */
    private function faqsQuery(): Builder
    {
        return Faq::queryModel();
    }
}
