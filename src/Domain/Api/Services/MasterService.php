<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Bank;
use App\Eloquent\BankBranch;
use App\Eloquent\JobCategory;
use App\Eloquent\Prefecture;
use App\Eloquent\Station;
use App\Exceptions\APIRuntimeException;
use Illuminate\Support\Collection;
use Src\Domain\Api\Models\Bank\BankBranchDetail;
use Src\Domain\Api\Models\Bank\BankDetail;
use Src\Domain\Api\Models\JobCategory\JobCategoryDetail;
use Src\Domain\Api\Models\Prefecture\PrefectureDetail;
use Src\Domain\Api\Models\Station\StationDetail;
use Storage;

/**
 * Class MasterService
 * @package Src\Domain\Api\Services
 */
class MasterService
{
    /**
     * Find a prefecture by its zipcode.
     *
     * @param string|null $zip_code The zipcode to search for.
     * @return array The detailed API response of the prefecture.
     */
    public function findWithZipcode(?string $zip_code): array
    {
        if (!$zip_code) {
            throw new APIRuntimeException('api_errors.prefecture.required');
        }
        /** @var Prefecture $prefecture */
        $prefecture = Prefecture::queryModel()->where('zip_code', $zip_code)->first();

        $stations = Station::queryModel()->where('zip_code', $zip_code)->get();

        if (!$prefecture) {
            throw new APIRuntimeException('api_errors.prefecture.not_found');
        }
        return [
            'prefecture' => (new PrefectureDetail($prefecture))->toDetailApiResponse(),
            'stations' => $stations->transform(function (Station $station) {
                return (new StationDetail($station))->toDetailApiResponse();
            }),
        ];
    }

    /**
     * Fetch all job categories
     *
     * @return Collection
     */
    public function fetchJobCategories(): Collection
    {
        $categories = JobCategory::queryModel()->get();

        return $categories->transform(function ($jobCategory) {
            return (new JobCategoryDetail($jobCategory))->toDetailApiResponse();
        });
    }

    public function listBanks(): Collection
    {
        $banks = Bank::queryModel()->get();

        return $banks->transform(function ($bank) {
            return (new BankDetail($bank))->toDetailApiResponse();
        });
    }

    public function listBankBranches(string $code): Collection
    {
        $branches = BankBranch::queryModel()->where('bank_code', $code)->get();

        return $branches->transform(function ($bank) {
            return (new BankBranchDetail($bank))->toDetailApiResponse();
        });
    }
}
