<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Job;
use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Src\Domain\Api\Models\Job\JobDetail;
use Src\Domain\Api\Models\Job\JobSearchForm;
use Src\Domain\Api\Models\JobApplication\JobApplicationForm;
use Src\Enums\ApprovalStatus;
use Src\Enums\PeriodType;
use Src\Enums\FileDiv;
use Src\Traits\StorageFileTrait;
use Throwable;

/**
 * Class JobService
 * @package Src\Domain\Api\Services
 */
class JobService
{
    use StorageFileTrait;

    /**
     * @param JobSearchForm $search_form
     * @return array
     */
    public function fetchAll(JobSearchForm $search_form): array
    {
        $isRecommended = optional(Auth::guard('api')->user())->is_recommended ?? false;

        $query = $this->jobWithRelationshipQuery()
            ->isPublic()->jobOpen()->isRecommended($isRecommended)->orderBy('created_at', 'desc');

        $query = $search_form->searchConditions($query);
        $paginator = $query->paginate(10);

        $paginator->getCollection()->transform(function ($job) {
            return (new JobDetail($job))->toListApiResponse();
        });

        return pagination($paginator);
    }

    /**
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        $job = $this->jobWithRelationshipQuery()->with(['favorites', 'jobApply'])
            ->isPublic()->findOrFail($id);

        return (new JobDetail($job))->toDetailApiResponse();
    }

    /**
     *
     * @param User $user
     * @return array
     */
    public function listJobUserApplied(User $user): array
    {
        $paginator = $user->jobs()->paginate(10);

        $paginator->getCollection()->transform(function ($job) {
            return (new JobDetail($job))->toListApiResponse();
        });

        return pagination($paginator);
    }

    /**
     *
     * @param User $user
     * @param int $job_id
     * @return bool
     */
    public function cancelJobApplied(User $user, int $job_id): bool
    {
        $result = false;
        $userJobApplied = $user->jobs->firstWhere(fn($apply) =>
            $apply->pivot->user_id === $user->id && $apply->pivot->job_id === $job_id
        );

        if (!$userJobApplied) {
            throw new APIRuntimeException('api_errors.job.job_have_not_apply');
        }

        /**
         * If the user disabled
         */
        if ($user->is_disable) {
            throw new APIRuntimeException('api_errors.job.is_disabled');
        }

        try {
            $result = DB::transaction(function () use ($user, $userJobApplied) {
                if($userJobApplied->pivot->approval_status == ApprovalStatus::APPROVED) {
                    $jobStartAt = Carbon::parse($userJobApplied->job_start_at);
                    /**
                     * Compare times to get the difference in hours.
                     */
                    $diffInHours = now()->diffInHours($jobStartAt);
                    /**
                     * Calculator point
                     */
                    $pointUser = $this->pointUser($diffInHours, $user);
                    /**
                     * Check the point plus cases
                     */
                    if ($pointUser->isPlusPoint) {
                        $user->pointHistory()->create($pointUser->dataUserPointHistory);
                        $user->update($pointUser->dataUser);
                        $user->jobs()->detach([$userJobApplied->id]);
                        if ($userJobApplied->is_filled) {
                            $userJobApplied->update(['is_filled' => false]);
                        }

                    }
                }

                return true;
            });
        } catch (Throwable $e) {
            Log::info($e);
        }

        return $result;
    }

    private function pointUser(float $diffInHours, User $user): object
    {
        $point = $user->point;

        $pointUser = (object) [
            'dataUser' => [
                'point' => $point,
            ],
            'isPlusPoint' => false,
        ];

        $pointRanges = [
            ['min' => -INF, 'max' => 0,   'point' => 16],
            ['min' => 0,    'max' => 6,   'point' => 8],
            ['min' => 6,    'max' => 12,  'point' => 6],
            ['min' => 12,   'max' => 24,  'point' => 4],
            ['min' => 24,   'max' => 48,  'point' => 2],
        ];

        foreach ($pointRanges as $range) {
            if ($diffInHours >= $range['min'] && $diffInHours <= $range['max']) {
                $pointUser->isPlusPoint = true;
                $newPoint = $point + $range['point'];
                $pointUser->dataUser['point'] = $newPoint;
                $pointUser->dataUserPointHistory = [
                    'user_id' => $user->id,
                    'point' => $point,
                    'new_point' => $newPoint,
                ];
            }
        }
        /**
         * Disable the user when the point is greater than 16
         */
        if ($pointUser->dataUser['point'] > MAX_POINT) {
            $pointUser->dataUser['is_disable'] = true;
            $pointUser->dataUser['disable_until_at'] = Carbon::now()->addDays(7);
        }

        return $pointUser;
    }

    /**
     * Apply To Job
     * @param JobApplicationForm $form
     * @param User $user
     * @param int $jobId
     * @return array|false|mixed
     * @throws Throwable
     */
    public function apply(JobApplicationForm $form, User $user ,int $jobId): mixed
    {
        $result = false;

        /**
         * If the user disabled for less than one week
         */
        if ($user->is_disable && $user->disable_until_at <= now()) {
            throw new APIRuntimeException('api_errors.job.is_disabled', [
                    'disable_until_at' => $user->disable_until_at
                ]);
        }

        /**
         * Check accommodation information is missing
         */
        if (!$user->residenceCard) {
            throw new APIRuntimeException('api_errors.residence_card.requires_admin_approval');
        }

        /**
         * Check period of stay
         */
        if($user->residenceCard->period_of_stay < now()){
            throw new APIRuntimeException('api_errors.residence_card.expired');
        }

        /**
         * Check period type of stay
         */
        if ($user->residenceCard->period_type === PeriodType::OTHER) {
            throw new APIRuntimeException('api_errors.residence_card.other_type_not_allowed');
        }

        // Check if the job exists
        /** @var Job $job */
        $job = $this->jobQuery()->findOrFail($jobId);
        if (!$job) {
            throw new APIRuntimeException('api_errors.job.job_not_found');
        }
        if ($job->is_filled) {
            throw new APIRuntimeException('api_errors.job.job_is_filled');
        }


        // Check if the user has already applied for the job
        if ($user->jobs()->where('job_id',$jobId)->exists()) {
            throw new APIRuntimeException('api_errors.job.user_has_already_applied_for_this_job');
        }

        /**
         *  User with is_recommended = false are not allowed to apply for job where is_recommended = true
         */

        if (!$user->is_recommended && $job->is_recommended) {
            throw new APIRuntimeException('api_errors.job.is_recommended');
        }

        try {
            $result = DB::transaction(function () use ($form, $user, $jobId) {
                $attributes = [
                    'approval_status' => ApprovalStatus::WAITING
                ];

                if($form->getResumeId()){
                    $uploadedFile = $this->createStorageFile($form->getResumeId(), FileDiv::IMAGE,"$user->code/resume");
                    if ($uploadedFile) {
                        $attributes['resume_id'] = $uploadedFile;
                    }
                }
                /**
                 * If a user is disabled for more than one week, their points will be reset.
                 */
                if ($user->is_disable && $user->disable_until_at > now()) {
                    $user->pointHistory()->create([
                        'user_id' => $user->id,
                        'point' => $user->point,
                        'new_point' => 0,
                    ]);
                    $user->update([
                        'is_disable' => false,
                        'point' => 0,
                        'disable_until_at' => null
                    ]);
                }

                /**
                 * Add record to t_job_applications
                 */
                $user->jobs()->attach($jobId, $attributes);

                logger()->info('Job application submitted successfully.');

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Error occurred while submitting job application', [], $e);
        }

        return $result;
    }

    /**
     * Favorite To Job
     *
     * @param int $userId
     * @param int $jobId
     * @return array|false|mixed
     */
    public function favorite(int $userId, int $jobId): mixed
    {
        $result = false;

        // Check if the user exists
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($userId);
        if (!$user) {
            throw new APIRuntimeException('api_errors.user.not_found');
        }

        // Check if the job exists
        /** @var Job $recruitmentJob */
        $job = $this->jobQuery()->findOrFail($jobId);
        if (!$job) {
            throw new APIRuntimeException('api_errors.job.job_not_found');
        }

        if ($user->jobFavorites()->where('job_id', $job->id)->exists()) {
            throw new APIRuntimeException('api_errors.job.user_has_already_favorite_this_job');
        }

        try {
            $result = DB::transaction(function () use ( $user, $jobId) {
                $user->jobFavorites()->attach($jobId);
                logger()->info('Job favorite submitted successfully.');

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Error occurred while submitting job favorite', ['user_id' => $userId, 'job_id' => $jobId], $e);
        }

        return $result;
    }

    /**
     * Favorite To Job
     *
     * @param int $userId
     * @param int $jobId
     * @return array|false|mixed
     */
    public function remove(int $userId, int $jobId): mixed
    {
        $result = false;

        // Check if the user exists
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($userId);
        if (!$user) {
            throw new APIRuntimeException('api_errors.user.not_found');
        }

        // Check if the job exists
        /** @var Job $recruitmentJob */
        $job = $this->jobQuery()->findOrFail($jobId);
        if (!$job) {
            throw new APIRuntimeException('api_errors.job.job_not_found');
        }

        try {
            $result = DB::transaction(function () use ($user, $jobId) {
                $user->jobFavorites()->detach($jobId);

                logger()->info('Job favorite removed successfully');

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Error occurred while submitting job favorite: ', [], $e);
        }

        return $result;
    }

    /**
     * @return Builder|User
     */
    private function userQuery(): Builder|User
    {
        return User::query()->getModel();
    }

    /**
     * @return Builder
     */
    public function jobWithRelationshipQuery(): Builder
    {
        return $this->jobQuery()->with(['category', 'thumbnail']);
    }

    /**
     * Get the job query builder.
     *
     * @return Job|Builder
     */
    private function jobQuery(): Job|Builder
    {
        return Job::queryModel();
    }
}
