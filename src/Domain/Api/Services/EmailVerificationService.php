<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;

/**
 * Class EmailVerificationService
 * @package Src\Domain\Api\Services
 */
class EmailVerificationService
{
    /**
     * Login
     *
     * @param string $token
     * @return mixed
     */
    public function verificationEmail(string $token): mixed
    {
        $user = $this->userQuery()->where('email_verification_token', $token)->first();

        if (! $user) {
            throw new APIRuntimeException('api_errors.user.link_does_not_exist');
        }

        return $user->markEmailAsVerified();
    }

    private function userQuery()
    {
        return User::queryModel();
    }
}
