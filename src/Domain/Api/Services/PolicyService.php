<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Policy;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Policy\PolicyDetail;

/**
 * Class PolicyService
 * @package Src\Domain\Api\Services
 */
class PolicyService
{
    /**
     * List Policy
     *
     *
     * @return array
     */
    public function fetchAll(): array
    {
        $paginator = $this->policyQuery()->isPublic()->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($faq) {
            return (new PolicyDetail($faq))->toDetailApiResponse();
       });

        return pagination($paginator);
    }

    /**
     * Get Policy Detail
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        $faq = $this->policyQuery()->isPublic()->findOrFail($id);

        return (new PolicyDetail($faq))->toDetailApiResponse();

    }

    /**
     *
     * @return Builder
     */
    private function policyQuery(): Builder
    {
        return Policy::queryModel();
    }
}
