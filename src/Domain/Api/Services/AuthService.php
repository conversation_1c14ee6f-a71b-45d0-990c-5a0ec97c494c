<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\StorageFile;
use App\Eloquent\TmpResidenceCard;
use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;
use App\Exceptions\ErrorException;
use App\Mail\VerifyEmailRegister;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Src\Domain\Api\Models\Auth\LoginForm;
use Src\Domain\Api\Models\User\UserDetail;
use Src\Domain\Api\Models\User\UserForm;
use Src\Enums\PeriodType;
use Src\Enums\UserBankType;
use Src\Enums\UserStatus;
use Src\Traits\Utils\FileUpload\FileUploadable;
use Throwable;

/**
 * Class AuthService
 * @package Src\Domain\Api\Services
 */
class AuthService
{
    use FileUploadable;
    /**
     * Login
     *
     * @param LoginForm $form
     * @return false|mixed
     */
    public function login(LoginForm $form)
    {
        $result = false;
        $credentials = [
            'email' => $form->getEmail(),
            'password' => $form->getPassword()
        ];
        if (!auth('user')->attempt($credentials)) {
            logger()->debug('Unauthorized', $form->toArray());
            return false;
        }
        /** @var User $user */
        $user = auth('user')->user();
        if (! $user->hasVerifiedEmail()) {
            throw new APIRuntimeException('api_errors.auth.not_verify_email');
        }

        if ($user->user_status !== UserStatus::APPROVED) {
            throw new APIRuntimeException('api_errors.auth.not_approved');
        }

        try {
            $result = DB::transaction(static function () use ($form, $user) {
                $token_result = $user->createToken('Personal Access Token');
                $token = $token_result->token;
                $token->expires_at = Carbon::now()->addYear();
                $token->save();

                return [
                    'apiToken' => $token_result->accessToken,
                    'tokenType' => 'Bearer',
                    'expiresAt' => Carbon::parse($token->expires_at)->toDateTimeString(),
                    'user' => (new UserDetail($user))->getBaseInfo(),
                ];
            });
        } catch (Throwable $e) {
            logger_error('Failed to login: ', ['form_data' => $form->toArray()], $e);
        }

        return $result;
    }

    /**
     * Sign up
     *
     * @param UserForm $form
     * @return boolean
     */
    public function signUp(UserForm $form): bool
    {
        $result = false;
        $uploadedFiles = [];

        try {
            $result = DB::transaction(function () use ($form, &$uploadedFiles) {
                $codeUser = $this->userQuery()->get()->pluck('code')->toArray();
                $newCode = gen_code($codeUser, 6);
                $userAttribute = $this->prepareUserAttributes($form, $newCode, $uploadedFiles);
                /** @var User $user */
                $user = $this->userQuery()->createOrThrow($userAttribute);
                if ($user) {
                    if ($userAttribute['bank_type'] === UserBankType::BANK) {
                        $this->updateOrCreateUserBank($user, $form, $newCode,$uploadedFiles);
                    }
                    $this->createResidenceCardTmp($user, $form, $newCode, $uploadedFiles);
                }

                Mail::to($user->email)->send(new VerifyEmailRegister($user->email_verification_token, $user->name));

                return true;
            });
        } catch (Throwable $e) {
            \Log::info($e);
        }

        // Delete file when error
       $this->deleteImageError($result, $uploadedFiles);

        return $result;
    }

    public function deleteImageError(bool $result,array $uploadedFiles): void
    {
        if (!$result && !empty($uploadedFiles)) {
            foreach ($uploadedFiles as $fileId) {
                try {
                    /** @var StorageFile $storage_file */
                    $storage_file = $this->storageFileQuery()->find($fileId);
                    if ($storage_file) {
                        $this->deleteFileFromStorage($storage_file->file_path);
                        $storage_file->forceDelete();
                    }
                } catch (Throwable $deleteError) {
                    \Log::info($deleteError);
                }
            }
        }
    }

    /**
     * @param UserForm $form
     * @param string $code
     * @param array $uploadedFiles
     * @return array
     * @throws ErrorException
     */
    public function prepareUserAttributes(UserForm $form, string $code, array &$uploadedFiles): array
    {
        $attributes = $form->createUserAttributes();
        $attributes['code'] = $code;
        $attributes['user_status'] = UserStatus::INITIAL;
        $attributes['email_verification_token'] = Str::random(64);
        $fileFields = [
            'AvatarId' => 'avatar_id',
            'HealthCertificateId' => 'health_certificate_id',
            'PassportImageId' => 'passport_image_id',
        ];

        foreach ($fileFields as $formField => $dbField) {
            $file = $form->{"get$formField"}();
            if ($file) {
                $uploadedFile = $this->createStorageFile($file, "$code/info");
                if ($uploadedFile) {
                    $attributes[$dbField] = $uploadedFile['storage_file_id'];
                    $uploadedFiles[] = $uploadedFile['storage_file_id'];
                }
            }
        }
        return $attributes;
    }

    /**
     * @param User $user
     * @param UserForm $form
     * @param string $code
     * @param array $uploadedFiles
     * @param bool $isRegister
     * @return void
     * @throws ErrorException
     */
    public function createResidenceCardTmp(User $user, UserForm $form, string $code, array &$uploadedFiles, bool $isRegister = true): void
    {
        $attributes = $form->createResidenceCardsTmpAttributes();
        $attributes['user_id'] = $user->id;
        $fileFields = [
            'FrontCardId' => 'front_card_id',
            'BackCardId' => 'back_card_id',
            'FrontIdentificationId' => 'front_identification_id',
            'BackIdentificationId' => 'back_identification_id',
        ];

        foreach ($fileFields as $formField => $dbField) {
            $file = $form->{"get$formField"}();
            if ($file) {
                $uploadedFile = $this->createStorageFile($file, "$code/residence_card");
                if ($uploadedFile) {
                    $attributes[$dbField] = $uploadedFile['storage_file_id'];
                    $uploadedFiles[] = $uploadedFile['storage_file_id'];
                }
            }
        }
        $attributes['school_name'] = $attributes['period_type'] === PeriodType::STUDENT
            ? $form->getSchoolName()
            : null;

        // Register user
        if ($isRegister) {
            $user->tmpResidenceCards()->updateOrCreate([], $attributes);
        } else {
            //Update profile
            $residenceCard = $user->residenceCard;

            if ($residenceCard) {
                $residenceCard->fill($attributes);
                if($residenceCard->isDirty()){
                    $user->tmpResidenceCards()->updateOrCreate([], $attributes);
                }
            }
        }
    }

    public function updateOrCreateUserBank(User $user, UserForm $form,string $newCode,array &$uploadedFiles): void
    {
        $userBankAttribute = $form->createUserBankAttributes();

        if ($form->getAtmImageId()) {
            $uploadedFile = $this->createStorageFile($form->getAtmImageId(), "$newCode/atm_card");
            if ($uploadedFile) {
                $userBankAttribute['atm_image_id'] = $uploadedFile['storage_file_id'];
                $uploadedFiles[] = $uploadedFile['storage_file_id'];
            }
        }
        $user->userBank()->updateOrCreate([], $userBankAttribute);
    }
    /**
     * @throws ErrorException
     */
    public function createStorageFile($fileUpload, $path): false|array
    {
        if (!$fileUpload) {
            return false;
        }

        $file = $this->putFileToStorage($fileUpload, $path);
        /** @var StorageFile $storage_file */
        $storage_file = $this->storageFileQuery()->createOrThrow($file);

        return [
            'storage_file_id' => $storage_file->id,
        ];
    }

    /**
     * Verify the email before creating a user account
     *
     * @param string $email
     * @return bool
     */
    public function checkExistEmail(string $email): bool
    {
        return (bool) $this->userQuery()->where("email", $email)->first();
    }

    private function userQuery()
    {
        return User::queryModel();
    }

    /**
     *  storageFileQuery
     */
    private function storageFileQuery()
    {
        return StorageFile::queryModel();
    }
}
