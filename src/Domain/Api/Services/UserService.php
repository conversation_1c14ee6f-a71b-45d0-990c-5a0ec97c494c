<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Src\Domain\Api\Models\User\UserDetail;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Src\Domain\Api\Models\User\UserForm;
use Src\Enums\UserBankType;
use Throwable;

/**
 * Class UserService
 * @package Src\Domain\Api\Services
 */
class UserService
{
    /**
     * Get the current authenticated user.
     *
     * @param User $author
     * @return array
     */
    public function getCurrentUser(User $author): array
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($author->id);
        return (new UserDetail($user))->toDetailResponse();
    }

    /**
     *
     * @param UserForm $form
     * @param int $user_id
     * @return bool
     */
    public function updateProfile(UserForm $form ,int $user_id): bool
    {
        $result = false;
        $authService = new AuthService();
        $uploadedFiles = [];

        try {
            $result = DB::transaction(function () use ($form, $user_id, $authService, &$uploadedFiles) {

                $user = $this->userQuery()->findOrFail($user_id);
                $newCode = $user->code;

                $userAttribute = $authService->prepareUserAttributes($form, $newCode, $uploadedFiles);
                $keysToRemove = ['email', 'code', 'user_status', 'email_verification_token'];
                if (empty($userAttribute['password'])) {
                    $keysToRemove[] = 'password';
                }
                foreach ($keysToRemove as $key) {
                    unset($userAttribute[$key]);
                }
                $user->update($userAttribute);

                if ($userAttribute['bank_type'] === UserBankType::BANK) {
                    //Update user bank
                    $authService->updateOrCreateUserBank($user, $form, $newCode,$uploadedFiles);
                } else {
                    // Delete user bank when bank_type is CASH
                    $user->userBank()?->delete();
                }

                $authService->createResidenceCardTmp($user, $form, $newCode, $uploadedFiles, false);

                return true;
            });
        } catch (Throwable $e) {
            Log::error($e);
        }

        //Remove uploaded image if profile update fails
        $authService->deleteImageError($result, $uploadedFiles);

        return $result;
    }

    /**
     * Get the user query builder.
     *
     * @return User|Builder
     */
    private function userQuery(): User|Builder
    {
        return User::query();
    }
}
