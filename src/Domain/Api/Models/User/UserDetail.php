<?php

namespace Src\Domain\Api\Models\User;

use App\Eloquent\User;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;

/**
 * Class UserDetail
 * @package Src\Domain\Api\Models\User
 */
class UserDetail extends FormModel
{
    /**
     * @var User
     */
    protected User $user;

    /**
     * UserDetail constructor.
     * @param User $user
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->user->id;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->user->code;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->user->name;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->user->email;
    }

    /**
     * @return string
     */
    public function getPassword(): string
    {
        return $this->user->password;
    }

    /**
     * @return int|null
     */
    public function getAvatarId(): ?int
    {
        return $this->user->avatar_id;
    }

    /**
     * @return string|null
     */
    public function getAvatarUrl(): ?string
    {
        $path = optional($this->user->avatar)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return int|null
     */
    public function getHealthCertificateId(): ?int
    {
        return $this->user->health_certificate_id;
    }

    /**
     * @return string|null
     */
    public function getHealthCertificateUrl(): ?string
    {
        $path = optional($this->user->healthCertificate)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getUserStatus(): ?string
    {
        return $this->user->user_status;
    }

    /**
     * @return string | null
     */
    public function getNameKana(): ?string
    {
        return $this->user->name_kana;
    }

    /**
     * @return string | null
     */
    public function getNameKanji(): ?string
    {
        return $this->user->name_kanji;
    }

    /**
     * @return string | null
     */
    public function getPhoneNumber(): ?string
    {
        return $this->user->phone_number;
    }

    /**
     * @return string | null
     */
    public function getGender(): ?string
    {
        return $this->user->gender;
    }

    /**
     * @return string | null
     */
    public function getBirthday(): ?string
    {
        return $this->user->birthday;
    }

    /**
     * @return string | null
     */
    public function getNationality(): ?string
    {
        return $this->user->nationality;
    }

    /**
     * @return bool | null
     */
    public function getHasCertificate(): ?bool
    {
        return $this->user->has_certificate;
    }

    /**
     * @return string | null
     */
    public function getJapaneseLevel(): ?string
    {
        return $this->user->japanese_level;
    }

    /**
     * @return string | null
     */
    public function getArrivalDate(): ?string
    {
        return $this->user->arrival_date;
    }

    /**
     * @return string | null
     */
    public function getComingDate(): ?string
    {
        return $this->user->coming_date;
    }

    /**
     * @return string | null
     */
    public function getZipCode(): ?string
    {
        return $this->user->zip_code;
    }

    /**
     * @return string | null
     */
    public function getPrefecture(): ?string
    {
        return $this->user->prefecture;
    }


    /**
     * @return string | null
     */
    public function getStreetAddress(): ?string
    {
        return $this->user->street_address;
    }

    /**
     * @return string | null
     */
    public function getTrainStationName(): ?string
    {
        return $this->user->train_station_name;
    }

    /**
     * @return string | null
     */
    public function getEmergencyName(): ?string
    {
        return $this->user->emergency_name;
    }

    /**
     * @return string | null
     */
    public function getEmergencyRelation(): ?string
    {
        return $this->user->emergency_relation;
    }

    /**
     * @return string | null
     */
    public function getEmergencyPhoneNumber(): ?string
    {
        return $this->user->emergency_phone_number;
    }

    /**
     * @return string | null
     */
    public function getBankType(): ?string
    {
        return $this->user->bank_type;
    }

    /**
     * @return string | null
     */
    public function getIdentificationAt(): ?string
    {
        return $this->user->identification_at;
    }

    /**
     * @return bool | null
     */
    public function getIsDisable(): ?bool
    {
        return $this->user->is_disable;
    }

    /**
     * @return int | null
     */
    public function getPoint(): int
    {
        return $this->user->point;
    }

    /**
     * @return string|null
     */
    public function getPassportImageUrl(): ?string
    {
        $passport = optional($this->user->passport)->file_path;

        return $passport ? presigned_url($passport) : null;
    }

    /**
     * @return string | null
     */
    public function getPassportNumber(): ?string
    {
        return $this->user->passport_number;
    }

    /**
     * @return string | null
     */
    public function getPassportExpiredAt(): ?string
    {
        return $this->user->passport_expired_at;
    }

    /**
     * @return string | null
     */
    public function getBankCode(): ?string
    {
        return optional($this->user->userBank)->bank_code;
    }

    /**
     * @return string | null
     */
    public function getBranchCode(): ?string
    {
        return optional($this->user->userBank)->branch_code;
    }

    /**
     * @return string|null
     */
    public function getDepositType(): ?string
    {
        return optional($this->user->userBank)->deposit_type;
    }

    /**
     * @return string | null
     */
    public function getAccountName(): ?string
    {
        return optional($this->user->userBank)->account_name;
    }

    /**
     * @return string | null
     */
    public function getAccountNumber(): ?string
    {
        return $this->user->userBank->account_number ?? null;
    }

    /**
     * @return string | null
     */
    public function getATMImageUrl(): ?string
    {
        if (!$this->user->userBank || !$this->user->userBank->atmImage) {
            return null;
        }

        $path = optional($this->user->userBank->atmImage)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getTownAddress(): ?string
    {
        return $this->user->town_address;
    }

    /**
     * @return int
     */
    public function getNumberUnreadNotification(): int
    {
        return $this->user->number_unread_notification;
    }

    /**
     * @return string | null
     */
    public function getEmailVerificationToken(): ?string
    {
        return $this->user->email_verification_token;
    }

    /**
     * @return string | null
     */
    public function getEmailVerificationAt(): ?string
    {
        return $this->user->email_verification_at;
    }

    /**
     * @return string | null
     */
    public function getFrontCardUrl(): ?string
    {
        if (!$this->user->residenceCard || !$this->user->residenceCard->frontCard) {
            return null;
        }

        $path = optional($this->user->residenceCard->frontCard)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getBackCardUrl(): ?string
    {
        if (!$this->user->residenceCard || !$this->user->residenceCard->backCard) {
            return null;
        }

        $path = optional($this->user->residenceCard->backCard)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getPeriodType(): ?string
    {
        return $this->user->residenceCard->period_type ?? null;
    }

    /**
     * @return string | null
     */
    public function getSchoolName(): ?string
    {
        return $this->user->residenceCard->school_name ?? null;
    }

    /**
     * @return string | null
     */
    public function getFrontIdentificationUrl(): ?string
    {
        if (!$this->user->residenceCard || !$this->user->residenceCard->frontIdentification) {
            return null;
        }

        $path = optional($this->user->residenceCard->frontIdentification)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getBackIdentificationUrl(): ?string
    {
        if (!$this->user->residenceCard || !$this->user->residenceCard->backIdentification) {
            return null;
        }

        $path = optional($this->user->residenceCard->backIdentification)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string | null
     */
    public function getIdentificationExpiredAt(): ?string
    {
        return $this->user->residenceCard->identification_expired_at ?? null;
    }

    /**
     * @return string | null
     */
    public function getPeriodOfStay(): ?string
    {
        return $this->user->residenceCard->period_of_stay ?? null;
    }

    /**
     * @return string | null
     */
    public function getPeriodExpireAt(): ?string
    {
        return $this->user->residenceCard->period_expire_at ?? null;
    }

    /**
     * @return array
     */
    public function getBaseInfo(): array
    {
        return [
            'id' => $this->getId(),
            'code' => $this->getCode(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'avatarUrl' => $this->getAvatarUrl(),
            'nameKana' => $this->getNameKana(),
            'nameKanji' => $this->getNameKanji(),
            'phoneNumber' => $this->getPhoneNumber(),
            'gender' => $this->getGender(),
            'birthday' => $this->getBirthday(),
            'nationality' => $this->getNationality(),
            'japaneseLevel' => $this->getJapaneseLevel(),
            'zipcode' => $this->getZipCode(),
            'prefecture' => $this->getPrefecture(),
            'streetAddress' => $this->getStreetAddress(),
            'townAddress' => $this->getTownAddress(),
            'trainStationName' => $this->getTrainStationName(),
            'userStatus' => $this->getUserStatus(),
            'numberUnreadNotification' => $this->getNumberUnreadNotification(),
        ];
    }


    /**
     * Attribute
     *
     * @return array
     */
    public function getUserBankAttributes(): array
    {
        return [
            'atmImageUrl' => $this->getATMImageUrl(),
            'bankCode' => $this->getBankCode(),
            'branchCode' => $this->getBranchCode(),
            'accountName' => $this->getAccountName(),
            'accountNumber' => $this->getAccountNumber(),
            'depositType' => $this->getDepositType(),
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function getUserResidenceCardAttributes(): array
    {
        return [
            'frontCardUrl' => $this->getFrontCardUrl(),
            'backCardUrl' => $this->getBackCardUrl(),
            'periodType' => $this->getPeriodType(),
            'schoolName' => $this->getSchoolName(),
            'frontIdentificationUrl' => $this->getFrontIdentificationUrl(),
            'backIdentificationUrl' => $this->getBackIdentificationUrl(),
            'identificationExpiredAt' => $this->getIdentificationExpiredAt(),
            'periodOfStay' => $this->getPeriodOfStay(),
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function toDetailResponse(): array
    {
        return [
            'id' => $this->getId(),
            'code' => $this->getCode(),
            'email' => $this->getEmail(),
            'name' => $this->getName(),
            'nameKana' => $this->getNameKana(),
            'nameKanji' => $this->getNameKanji(),
            'avatarUrl' => $this->getAvatarUrl(),
            'healthCertificateUrl' => $this->getHealthCertificateUrl(),
            'userStatus' => $this->getUserStatus(),
            'phoneNumber' => $this->getPhoneNumber(),
            'gender' => $this->getGender(),
            'birthday' => $this->getBirthday(),
            'nationality' => $this->getNationality(),
            'hasCertificate' => $this->getHasCertificate(),
            'japaneseLevel' => $this->getJapaneseLevel(),
            'arrivalDate' => $this->getArrivalDate(),
            'zipCode' => $this->getZipCode(),
            'prefecture' => $this->getPrefecture(),
            'streetAddress' => $this->getStreetAddress(),
            'townAddress' => $this->getTownAddress(),
            'trainStationName' => $this->getTrainStationName(),
            'emergencyName' => $this->getEmergencyName(),
            'emergencyRelation' => $this->getEmergencyRelation(),
            'emergencyPhoneNumber' => $this->getEmergencyPhoneNumber(),
            'bankType' => $this->getBankType(),
            'point' => $this->getPoint(),
            'passportImageUrl' => $this->getPassportImageUrl(),
            'passportNumber' => $this->getPassportNumber(),
            'passportExpiredAt' => $this->getPassportExpiredAt(),
            'numberUnreadNotification' => $this->getNumberUnreadNotification(),
            'identificationAt' => $this->getIdentificationAt(),
            'isDisable' => $this->getIsDisable(),
            'userBank' => $this->getUserBankAttributes(),
            'userResidenceCard' => $this->getUserResidenceCardAttributes(),
        ];
    }
}
