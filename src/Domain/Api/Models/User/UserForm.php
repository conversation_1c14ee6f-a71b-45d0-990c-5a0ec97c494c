<?php

namespace Src\Domain\Api\Models\User;

use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;

/**
 * Class LoginForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class UserForm extends FormModel
{
    protected string $name;
    protected string $email;
    protected ?string $password;
    protected ?UploadedFile $avatar_id;
    protected ?UploadedFile $health_certificate_id;
    protected ?string $name_kana;
    protected ?string $name_kanji;
    protected ?string $phone_number;
    protected ?string $gender;
    protected ?string $birthday;
    protected ?string $nationality;
    protected ?bool   $has_certificate;
    protected ?string $japanese_level;
    protected ?string $arrival_date;
    protected ?string $zip_code;
    protected ?string $prefecture;
    protected ?string $street_address;
    protected ?string $train_station_name;
    protected ?string $town_address;
    protected ?string $emergency_name;
    protected ?string $emergency_relation;
    protected ?string $emergency_phone_number;
    protected ?string $bank_type;
    protected ?string $bank_code;
    protected ?string $branch_code;
    protected ?UploadedFile $atm_image_id;

    protected ?string $deposit_type;
    protected ?string $account_name;
    protected ?string $account_number;
    protected ?UploadedFile $front_card_id;
    protected ?UploadedFile $back_card_id;
    protected ?string $period_type;
    protected ?string $school_name;
    protected ?UploadedFile $front_identification_id;
    protected ?UploadedFile $back_identification_id;
    protected ?string $period_of_stay;
    protected ?string $identification_expired_at;
    protected ?UploadedFile $passport_image_id;
    protected ?string $passport_number;
    protected ?string $passport_expired_at;

    /**
     * UserForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        foreach ($input as $key => $value) {
            if ($key && property_exists($this, $key)) {
                $this->$key = $value;
            }
        }
    }
    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return ?string
     */
    public function getPassword(): ?string
    {
        return $this->password ? bcrypt($this->password) : '';
    }

    /**
     * @return ?UploadedFile
     */
    public function getAvatarId(): ?UploadedFile
    {
        return $this->avatar_id;
    }

    /**
     * @return ?UploadedFile
     */
    public function getHealthCertificateId(): ?UploadedFile
    {
        return $this->health_certificate_id;
    }

    /**
     * @return string | null
     */
    public function getNameKana(): ?string
    {
        return $this->name_kana;
    }

    /**
     * @return string | null
     */
    public function getNameKanji(): ?string
    {
        return $this->name_kanji;
    }

    /**
     * @return string | null
     */
    public function getPhoneNumber(): ?string
    {
        return $this->phone_number;
    }

    /**
     * @return string | null
     */
    public function getGender(): ?string
    {
        return $this->gender;
    }

    /**
     * @return string | null
     */
    public function getBirthday(): ?string
    {
        return $this->birthday;
    }

    /**
     * @return string | null
     */
    public function getNationality(): ?string
    {
        return $this->nationality;
    }

    /**
     * @return bool | null
     */
    public function getHasCertificate(): ?bool
    {
        return $this->has_certificate;
    }

    /**
     * @return string | null
     */
    public function getJapaneseLevel(): ?string
    {
        return $this->japanese_level;
    }

    /**
     * @return string | null
     */
    public function getArrivalDate(): ?string
    {
        return $this->arrival_date;
    }

    /**
     * @return string | null
     */
    public function getZipCode(): ?string
    {
        return $this->zip_code;
    }

    /**
     * @return string | null
     */
    public function getPrefecture(): ?string
    {
        return $this->prefecture;
    }


    /**
     * @return string | null
     */
    public function getStreetAddress(): ?string
    {
        return $this->street_address;
    }

    /**
     * @return string|null
     */
    public function getTownAddress(): ?string
    {
        return $this->town_address;
    }

    /**
     * @return string | null
     */
    public function getTrainStationName(): ?string
    {
        return $this->train_station_name;
    }

    /**
     * @return string | null
     */
    public function getEmergencyName(): ?string
    {
        return $this->emergency_name;
    }

    /**
     * @return string | null
     */
    public function getEmergencyRelation(): ?string
    {
        return $this->emergency_relation;
    }

    /**
     * @return string | null
     */
    public function getEmergencyPhoneNumber(): ?string
    {
        return $this->emergency_phone_number;
    }

    /**
     * @return string | null
     */
    public function getBankType(): ?string
    {
        return $this->bank_type;
    }

    /**
     * @return string | null
     */
    public function getBanCode(): ?string
    {
        return $this->bank_code ?? null;
    }

    /**
     * @return string | null
     */
    public function getBranchCode(): ?string
    {
        return $this->branch_code ?? null;
    }

    /**
     * @return ?UploadedFile
     */
    public function getAtmImageId(): ?UploadedFile
    {
        return $this->atm_image_id ?? null;
    }

    /**
     * @return string|null
     */
    public function getDepositType(): ?string
    {
        return $this->deposit_type;
    }

    /**
     * @return string | null
     */
    public function getAccountName(): ?string
    {
        return $this->account_name ?? null;
    }

    /**
     * @return string | null
     */
    public function getAccountNumber(): ?string
    {
        return $this->account_number ?? null;
    }

    /**
     * @return ?UploadedFile
     */
    public function getFrontCardId(): ?UploadedFile
    {
        return $this->front_card_id;
    }

    /**
     * @return ?UploadedFile
     */
    public function getBackCardId(): ?UploadedFile
    {
        return $this->back_card_id;
    }

    /**
     * @return string | null
     */
    public function getPeriodType(): ?string
    {
        return $this->period_type;
    }

    /**
     * @return string | null
     */
    public function getSchoolName(): ?string
    {
        return $this->school_name;
    }

    /**
     * @return ?UploadedFile
     */
    public function getFrontIdentificationId(): ?UploadedFile
    {
        return $this->front_identification_id;
    }

    /**
     * @return ?UploadedFile
     */
    public function getBackIdentificationId(): ?UploadedFile
    {
        return $this->back_identification_id;
    }

    /**
     * @return string | null
     */
    public function getPeriodOfStay(): ?string
    {
        return $this->period_of_stay;
    }

    /**
     * @return string | null
     */
    public function getIdentificationExpireDAt(): ?string
    {
        return $this->identification_expired_at;
    }

    /**
     * @return ?UploadedFile
     */
    public function getPassportImageId(): ?UploadedFile
    {
        return $this->passport_image_id;
    }

    /**
     * @return string | null
     */
    public function getPassportNumber(): ?string
    {
        return $this->passport_number;
    }

    /**
     * @return string | null
     */
    public function getPassportExpiredAt(): ?string
    {
        return $this->passport_expired_at;
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function createUserAttributes(): array
    {
        return [
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'password' => $this->getPassword(),
            'name_kana' => $this->getNameKana(),
            'name_kanji' => $this->getNameKanji(),
            'phone_number' => $this->getPhoneNumber(),
            'gender' => $this->getGender(),
            'birthday' => $this->getBirthday(),
            'nationality' => $this->getNationality(),
            'has_certificate' => $this->getHasCertificate(),
            'japanese_level' => $this->getJapaneseLevel(),
            'arrival_date' => $this->getArrivalDate(),
            'zip_code' => $this->getZipCode(),
            'prefecture' => $this->getPrefecture(),
            'street_address' => $this->getStreetAddress(),
            'town_address' => $this->getTownAddress(),
            'train_station_name' => $this->getTrainStationName(),
            'emergency_name' => $this->getEmergencyName(),
            'emergency_relation' => $this->getEmergencyRelation(),
            'emergency_phone_number' => $this->getEmergencyPhoneNumber(),
            'bank_type' => $this->getBankType(),
            'passport_number' => $this->getPassportNumber(),
            'passport_expired_at' => $this->getPassportExpiredAt(),
            'email_verification_token' => str()->random(64),
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function createUserBankAttributes(): array
    {
        return [
            'bank_code' => $this->getBanCode(),
            'branch_code' => $this->getBranchCode(),
            'deposit_type' => $this->getDepositType(),
            'account_name' => $this->getAccountName(),
            'account_number' => $this->getAccountNumber(),
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function createResidenceCardsTmpAttributes(): array
    {
        return [
            'period_type' => $this->getPeriodType(),
            'period_of_stay' => $this->getPeriodOfStay(),
            'identification_expired_at' => $this->getIdentificationExpireDAt(),
        ];
    }

}
