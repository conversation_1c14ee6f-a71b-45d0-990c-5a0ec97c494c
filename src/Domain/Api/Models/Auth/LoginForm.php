<?php

namespace Src\Domain\Api\Models\Auth;

use Src\Domain\FormModel;

/**
 * Class LoginForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class LoginForm extends FormModel
{
    protected $email;

    protected $password;

    /**
     * LoginForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->email = $input['email'];
        $this->password = $input['password'];
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

}
