<?php

namespace Src\Domain\Api\Models\Password;

use Src\Domain\FormModel;

/**
 * Class LoginForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class PasswordForm extends FormModel
{
    /** @var string|null */
    protected $current_password;
    /** @var string|null */
    protected $new_password;
    /** @var string|null */
    protected $new_password_confirmation;

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->current_password = $input['current_password'];
        $this->new_password = $input['new_password'];
        $this->new_password_confirmation = $input['new_password_confirmation'];

    }

    /**
     * @return string
     */
    public function getCurrentPassword(): string
    {
        return $this->current_password;
    }

    /**
     * @return string
     */
    public function getNewPassword(): string
    {
        return $this->new_password;
    }

    /**
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'password' => bcrypt($this->getNewPassword())
        ];
    }
}
