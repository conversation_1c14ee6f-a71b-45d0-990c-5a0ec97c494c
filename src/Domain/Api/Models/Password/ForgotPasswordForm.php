<?php

namespace Src\Domain\Api\Models\Password;

use Src\Domain\FormModel;

/**
 * Class ForgotPasswordForm
 * @package Src\Domain\Api\Models\User\ForgotPasswordForm
 */
class ForgotPasswordForm extends FormModel
{
    /** @var string|null */
    protected $email;

    /** @var string|null */
    protected $birthday;

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->email = $input['email'];
        $this->birthday = $input['birthday'];

    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getBirthday(): ?string
    {
        return $this->birthday;
    }

    public function createAttributes(string $token)
    {
        return [
            'email' => $this->email,
            'token' => $token,
            'expired_at' => now()->addHours(24)
        ];
    }

    /**
     * @param $password
     * @return array
     */
    public function updateAttributes($password): array
    {
        return [
            'password' => $password
        ];
    }
}
