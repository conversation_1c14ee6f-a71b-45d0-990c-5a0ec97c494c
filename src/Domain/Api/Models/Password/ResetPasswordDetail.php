<?php

namespace Src\Domain\Api\Models\Password;

use App\Eloquent\UserResetPassword;
use Carbon\Carbon;
use Src\Domain\FormModel;

class ResetPasswordDetail extends FormModel
{
    protected $password_reset;

    /**
     * PasswordResetDetail constructor.
     * @param UserResetPassword $password_reset
     */
    public function __construct(UserResetPassword $password_reset)
    {
        $this->password_reset = $password_reset;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->password_reset->email;
    }

    /**
     * @return string
     */
    public function getToken(): string
    {
        return $this->password_reset->token;
    }

    /**
     * @return Carbon
     */
    public function getExpiredAt(): Carbon
    {
        return $this->password_reset->expired_at;
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'email' => $this->getEmail(),
            'token' => $this->getToken(),
            'expired_at' => $this->getExpiredAt()
        ];
    }
}
