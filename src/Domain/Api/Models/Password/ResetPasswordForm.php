<?php

namespace Src\Domain\Api\Models\Password;

use Src\Domain\FormModel;

/**
 * Class LoginForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class ResetPasswordForm extends FormModel
{
    /** @var string|null */
    protected $token;
    /** @var string|null */
    protected $new_password;
    /** @var string|null */
    protected $confirm_password;

    protected $fields = [
        'token' => 'string',
        'new_password' => 'string',
        'confirm_password' => 'string',
    ];

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->token = $input['token'];
        $this->new_password = $input['new_password'];
        $this->confirm_password = $input['confirm_password'];

    }

    /**
     * @return string|null
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     * @return string|null
     */
    public function getNewPassword(): ?string
    {
        return $this->new_password;
    }

    /**
     * @return string|null
     */
    public function getConfirmPassword(): ?string
    {
        return $this->confirm_password;
    }

    /**
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'password' => bcrypt($this->getNewPassword())
        ];
    }
}
