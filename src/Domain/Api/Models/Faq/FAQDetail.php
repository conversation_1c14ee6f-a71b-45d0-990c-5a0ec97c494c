<?php

namespace Src\Domain\Api\Models\Faq;

use App\Eloquent\Faq;
use App\Eloquent\Station;
use Src\Domain\FormModel;

/**
 * Class FAQDetail
 * @package Src\Domain\Api\Models\FAQDetail
 */
class FAQDetail extends FormModel
{
    /**
     * @var Faq
     */
    protected Faq $faq;

    /**
     * FAQDetail constructor.
     * @param Faq $faq
     */
    public function __construct(Faq $faq)
    {
        $this->faq = $faq;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->faq->id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->faq->type;
    }

    public function getQuestion(): string
    {
        return $this->faq->question;
    }

    /**
     * @return string
     */
    public function getAnswer(): string
    {
        return $this->faq->answer;
    }

    /**
     * @return int
     */
    public function getIsPublic(): int
    {
        return $this->faq->is_public;
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'type' => $this->getType(),
            'question' => $this->getQuestion(),
            'answer' => $this->getAnswer(),
            'is_public' => $this->getIsPublic(),
        ];
    }
}
