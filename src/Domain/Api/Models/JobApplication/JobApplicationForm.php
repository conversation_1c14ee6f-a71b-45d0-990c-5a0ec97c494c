<?php

namespace Src\Domain\Api\Models\JobApplication;

use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;
use Src\Enums\ApprovalStatus;

/**
 * Class JobApplicationForm
 * @package Src\Domain\Api\Models\JobApplication\Form
 */
class JobApplicationForm extends FormModel
{

    /** @var string|null */
    protected $resume_id;

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->resume_id = $input['resume_id'] ?? null;
    }

    /**
     * @return UploadedFile|null
     */
    public function getResumeId(): ?UploadedFile
    {
        return $this->resume_id;
    }
}
