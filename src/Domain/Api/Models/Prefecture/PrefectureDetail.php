<?php

namespace Src\Domain\Api\Models\Prefecture;

use App\Eloquent\Prefecture;
use Src\Domain\FormModel;

/**
 * Class PrefectureDetail
 * @package Src\Domain\Api\Models\Prefecture
 */
class PrefectureDetail extends FormModel
{
    /**
     * @var Prefecture
     */
    protected Prefecture $prefecture;

    /**
     * PrefectureDetail constructor.
     * @param Prefecture $prefecture
     */
    public function __construct(Prefecture $prefecture)
    {
        $this->prefecture = $prefecture;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->prefecture->id;
    }

    /**
     * @return int
     */
    public function getZipcode(): int
    {
        return $this->prefecture->zip_code;
    }

    /**
     * @return string
     */
    public function getPrefecture(): string
    {
        return $this->prefecture->prefecture;
    }

    /**
     * @return string
     */
    public function getCity(): string
    {
        return $this->prefecture->city;
    }

    /**
     * @return string
     */
    public function getTownArea(): string
    {
        return $this->prefecture->town_area;
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'zipcode' => $this->getZipcode(),
            'prefecture' => $this->getPrefecture(),
            'city' => $this->getCity(),
            'townArea' => $this->getTownArea(),
        ];
    }
}
