<?php

namespace Src\Domain\Api\Models\Bank;

use App\Eloquent\BankBranch;

class BankBranchDetail
{
    protected BankBranch $bankBranch;

    public function __construct(BankBranch $bankBranch)
    {
        $this->bankBranch = $bankBranch;
    }

    public function getBankCode(): string
    {
        return $this->bankBranch->bank_code;
    }

    public function getBranchCode(): string
    {
        return $this->bankBranch->branch_code;
    }

    public function getName(): string
    {
        return $this->bankBranch->name;
    }

    public function getNameKana(): string
    {
        return $this->bankBranch->name_kana;
    }

    public function toDetailApiResponse(): array
    {
        return [
            'bankCode' => $this->getBankCode(),
            'branchCode' => $this->getBranchCode(),
            'name' => $this->getName(),
            'nameKana' => $this->getNameKana(),
        ];
    }
}
