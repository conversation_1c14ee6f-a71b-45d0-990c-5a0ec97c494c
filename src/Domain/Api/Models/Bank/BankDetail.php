<?php

namespace Src\Domain\Api\Models\Bank;

use App\Eloquent\Bank;

class BankDetail
{
    protected Bank $bank;

    public function __construct(Bank $bank)
    {
        $this->bank = $bank;
    }

    public function getCode(): string
    {
        return $this->bank->code;
    }

    public function getName(): string
    {
        return $this->bank->name;
    }

    public function getNameKana(): string
    {
        return $this->bank->name_kana;
    }

    public function toDetailApiResponse(): array
    {
        return [
            'code' => $this->getCode(),
            'name' => $this->getName(),
            'nameKana' => $this->getNameKana(),
        ];
    }
}
