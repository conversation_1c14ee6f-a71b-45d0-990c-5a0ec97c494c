<?php

namespace Src\Domain\Api\Models\JobCategory;

use App\Eloquent\JobCategory;
use Src\Domain\FormModel;

/**
 * Class JobCategoryDetail
 */
class JobCategoryDetail extends FormModel
{
    /**
     * @var JobCategory
     */
    protected JobCategory $jobCategory;

    /**
     * JobCategoryDetail constructor.
     * @param JobCategory $jobCategory
     */
    public function __construct(JobCategory $jobCategory)
    {
        $this->jobCategory = $jobCategory;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->jobCategory->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->jobCategory->name;
    }
    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
        ];
    }
}
