<?php

namespace Src\Domain\Api\Models\Notification;

use App\Eloquent\Notification;

class NotificationDetail
{
    /**
     * @var Notification
     */
    protected $notification;

    /**
     * NotificationDetail constructor.
     *
     * @param Notification $notification
     */
    public function __construct(Notification $notification)
    {
        $this->notification = $notification;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->notification->id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->notification->type;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->notification->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->notification->body;
    }

    /**
     * @return int|null
     */
    public function getJobId(): ?int
    {
        return $this->notification->job_id;
    }

    /**
     * @return string
     */
    public function getStartNotificationAt(): string
    {
        return $this->notification->start_notification_at;
    }

    /**
     * @return string|null
     */
    public function getEndNotificationAt(): ?string
    {
        return $this->notification->end_notification_at;
    }

    /**
     * @return bool
     */
    public function getIsRead(): bool
    {
        return $this->notification->pivot->is_read ?? false;
    }

    public function commonAttributes(): array
    {
        return [
            'id' => $this->getId(),
            'type' => $this->getType(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'start_notification_at' => $this->getStartNotificationAt(),
            'end_notification_at' => $this->getEndNotificationAt(),
        ];
    }

    /**
     * Get List API Response
     * @return array
     */
    public function toListAnnouncementResponse(): array
    {
        return $this->commonAttributes();
    }

    public function toListNotificationResponse()
    {
        return array_merge($this->commonAttributes(), [
            'job_id' => $this->getJobId(),
            'is_read' => $this->getIsRead()
        ]);
    }
}
