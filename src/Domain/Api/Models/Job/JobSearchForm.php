<?php

namespace Src\Domain\Api\Models\Job;

use Illuminate\Database\Eloquent\Builder;
use Src\Domain\FormModel;
use Src\Utils\Util;

/**
 * Class RecruitmentSearchForm
 *
 * This class represents a form of searching for jobs.
 *
 */
class JobSearchForm extends FormModel
{
    /**
     * @var int|null
     */
    protected ?int $category_id;


    /**
     * @var string|null
     */
    protected ?string $type;

    /**
     * JobSearchForm constructor.
     *
     * @param array $input The input data for initializing the form
     */
    public function __construct(array $input = [])
    {
        $this->type = array_get_string($input, 'type');
        $this->category_id = array_get_int($input, 'category_id');
    }

    /**
     * @return int|null
     */
    public function getCategoryId(): ?int
    {
        return $this->category_id;
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * Add search conditions to the query based on the form input.
     *
     * @param Builder $query
     * @return Builder
     */
    public function searchConditions(Builder $query): Builder
    {
        $conditions = [
            ['category_id', '=', $this->getCategoryId()],
            ['type', '=', $this->getType()]
        ];

        $query = Util::addSearchCondition($query, $conditions);

        return $query;
    }
}
