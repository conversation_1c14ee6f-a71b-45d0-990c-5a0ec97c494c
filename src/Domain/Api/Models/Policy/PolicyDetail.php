<?php

namespace Src\Domain\Api\Models\Policy;

use App\Eloquent\Policy;
use Src\Domain\FormModel;

/**
 * Class PolicyDetail
 * @package Src\Domain\Api\Models\PolicyDetail
 */
class PolicyDetail extends FormModel
{
    /**
     * @var Policy
     */
    protected Policy $policy;

    /**
     * FAQDetail constructor.
     * @param Policy $policy
     */
    public function __construct(Policy $policy)
    {
        $this->policy = $policy;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->policy->id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->policy->type;
    }

    public function getTitle(): string
    {
        return $this->policy->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->policy->body;
    }

    /**
     * @return int
     */
    public function getOrder(): int
    {
        return $this->policy->order;
    }

    /**
     * @return int
     */
    public function getIsPublic(): int
    {
        return $this->policy->is_public;
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'type' => $this->getType(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'order' => $this->getOrder(),
            'isPublic' => $this->getIsPublic(),
        ];
    }
}
