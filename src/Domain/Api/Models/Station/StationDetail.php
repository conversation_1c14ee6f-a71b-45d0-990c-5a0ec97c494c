<?php

namespace Src\Domain\Api\Models\Station;

use App\Eloquent\Station;
use Src\Domain\FormModel;

/**
 * Class StationDetail
 * @package Src\Domain\Api\Models\Station
 */
class StationDetail extends FormModel
{
    /**
     * @var Station
     */
    protected Station $station;

    /**
     * StationDetail constructor.
     * @param Station $station
     */
    public function __construct(Station $station)
    {
        $this->station = $station;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->station->id;
    }

    /**
     * @return int
     */
    public function getZipcode(): int
    {
        return $this->station->zip_code;
    }

    public function getName(): string
    {
        return $this->station->name;
    }

    /**
     * @return string
     */
    public function getAddress(): string
    {
        return $this->station->address;
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'zipcode' => $this->getZipcode(),
            'name' => $this->getName(),
            'address' => $this->getAddress(),
        ];
    }
}
