<?php

namespace Src\Domain\Api\Docs;

use OpenApi\Attributes as OA;

#[OA\Tag(
    name: 'Master',
    description: 'Master endpoints'
)]
class MasterDoc
{
    #[OA\Get(
        path: '/emergency-relation',
        summary: 'Get list emergency relation',
        tags: ['Master'],
        parameters: [
            new OA\Parameter(
                name: 'Accept-Language',
                description: 'Language code',
                in: 'header',
                required: false,
                schema: new OA\Schema(
                    type: 'string',
                    default: 'en',
                    enum: ['en', 'id', 'jp', 'mm', 'ne', 'vi']
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/MasterResponse')
            )
        ]
    )]
    public function getEmergencyRelation() {}

    #[OA\Get(
        path: '/countries',
        summary: 'Get list countries',
        tags: ['Master'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/MasterResponse')
            )
        ]
    )]
    public function getListCountries() {}

    #[OA\Get(
        path: '/zipcode-data',
        summary: 'Get Address information by zipcode',
        tags: ['Master'],
        parameters: [
            new OA\Parameter(
                name: 'zip_code',
                description: 'Zip code to search for prefecture',
                in: 'query',
                required: true,
                schema: new OA\Schema(
                    type: 'string',
                    maxLength: 7,
                    example: '1000001'
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/ZipcodeResponse')
            ),
            new OA\Response(
                response: 404,
                description: 'Not Found',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            )
        ]
    )]
    public function getPrefectureWithZipcode() {}

    #[OA\Get(
        path: '/job-categories',
        summary: 'Get list job categories',
        tags: ['Master'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/JobCategoryResponse')
            ),
            new OA\Response(
                response: 400,
                description: 'Bad Request',
                content: new OA\JsonContent(ref: '#/components/schemas/ErrorResponse')
            ),
        ]
    )]
    public function getJobCategory() {}

    #[OA\Get(
        path: '/banks',
        summary: 'Get list banks',
        tags: ['Master'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/BankResponse')
            ),
        ]
    )]
    public function getBanks() {}

    #[OA\Get(
        path: '/bank/{code}/branches',
        summary: 'Get list branches of bank',
        tags: ['Master'],
        parameters: [
            new OA\Parameter(
                name: 'code',
                description: 'Bank code',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'string',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/BankBranchesResponse')
            ),
        ]
    )]
    public function getBankBranches() {}
}
