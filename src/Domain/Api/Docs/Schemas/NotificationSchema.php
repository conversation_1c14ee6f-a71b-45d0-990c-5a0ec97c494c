<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;


#[OA\Schema(
    schema: 'AnnouncementItem',
    properties: [
        new OA\Property(property: 'id', type: 'integer'),
        new OA\Property(property: 'type', type: 'string'),
        new OA\Property(property: 'title', type: 'string'),
        new OA\Property(property: 'body', type: 'string'),
        new OA\Property(property: 'startNotificationAt', type: 'string', format: 'date-time'),
        new OA\Property(property: 'endNotificationAt', type: 'string', format: 'date-time'),

    ]
)]
#[OA\Schema(
    schema: 'AnnouncementResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/AnnouncementItem')
                ),
                new OA\Property(
                    property: 'paginator',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Paginator')
                ),
            ]
        )
    ]
)]

#[OA\Schema(
    schema: 'NotificationItem',
    properties: [
        new OA\Property(property: 'id', type: 'integer'),
        new OA\Property(property: 'type', type: 'string'),
        new OA\Property(property: 'title', type: 'string'),
        new OA\Property(property: 'body', type: 'string'),
        new OA\Property(property: 'jobId', type: 'string'),
        new OA\Property(property: 'isRead', type: 'boolean'),
        new OA\Property(property: 'startNotificationAt', type: 'string', format: 'date-time'),
        new OA\Property(property: 'endNotificationAt', type: 'string', format: 'date-time'),

    ]
)]

#[OA\Schema(
    schema: 'ListNotificationResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/NotificationItem')
                ),
                new OA\Property(
                    property: 'paginator',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Paginator')
                ),
            ]
        ),
    ]
)]

#[OA\Schema(
    schema: 'NotificationUserResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'array',
            items: new OA\Items(ref: '#/components/schemas/NotificationItem')
        ),
    ]
)]
class NotificationSchema {}
