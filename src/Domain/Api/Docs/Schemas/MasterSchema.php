<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'MasterResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'label', type: 'string'),
                    new OA\Property(property: 'value', type: 'string'),
                ],
                type: 'object'
            )
        ),
    ]
)]
#[OA\Schema(
    schema: 'ZipcodeResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(
                    property: 'prefecture',
                    properties: [
                        new OA\Property(property: 'id', type: 'number'),
                        new OA\Property(property: 'zipcode', type: 'string'),
                        new OA\Property(property: 'prefecture', type: 'string'),
                        new OA\Property(property: 'city', type: 'string'),
                        new OA\Property(property: 'townArea', type: 'string'),
                    ],
                ),
                new OA\Property(
                    property: 'stations',
                    type: 'array',
                    items: new OA\Items(
                        properties: [
                            new OA\Property(property: 'id', type: 'number'),
                            new OA\Property(property: 'zipcode', type: 'string'),
                            new OA\Property(property: 'name', type: 'string'),
                            new OA\Property(property: 'address', type: 'string'),
                        ]
                    )
                ),
            ]
        ),
    ]
)]

#[OA\Schema(
    schema: 'JobCategoryResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer'),
                    new OA\Property(property: 'name', type: 'string'),
                ],
                type: 'object'
            )
        ),
    ]
)]

#[OA\Schema(
    schema: 'BankResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer'),
                    new OA\Property(property: 'code', type: 'string'),
                    new OA\Property(property: 'name', type: 'string'),
                    new OA\Property(property: 'nameKana', type: 'string'),
                ],
                type: 'object'
            )
        ),
    ]
)]

#[OA\Schema(
    schema: 'BankBranchesResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'array',
            items: new OA\Items(
                properties: [
                    new OA\Property(property: 'id', type: 'integer'),
                    new OA\Property(property: 'bankCode', type: 'string'),
                    new OA\Property(property: 'branchCode', type: 'string'),
                    new OA\Property(property: 'name', type: 'string'),
                    new OA\Property(property: 'nameKana', type: 'string'),
                ],
                type: 'object'
            )
        ),
    ]
)]
class MasterSchema {}
