<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'SuccessResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(property: 'result_detail', type: 'object')
    ]
)]
#[OA\Schema(
    schema: 'ErrorResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 400),
        new OA\Property(
            property: 'result_error',
            properties: [
                new OA\Property(property: 'message', type: 'string')
            ],
            type: 'object'
        )
    ]
)]
#[OA\Schema(
    schema: 'AuthorizedResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 401),
        new OA\Property(
            property: 'result_error',
            properties: [
                new OA\Property(property: 'message', type: 'string')
            ],
            type: 'object'
        )
    ]
)]
#[OA\Schema(
    schema: 'Paginator',
    properties: [
        new OA\Property(property: 'currentPage', type: 'number'),
        new OA\Property(property: 'firstPageUrl', type: 'string'),
        new OA\Property(property: 'from', type: 'number'),
        new OA\Property(property: 'lastPage', type: 'number'),
        new OA\Property(property: 'lastPageUrl', type: 'string'),
        new OA\Property(property: 'nextPageUrl', type: 'string'),
        new OA\Property(property: 'path', type: 'string'),
        new OA\Property(property: 'perPage', type: 'number'),
        new OA\Property(property: 'prevPageUrl', type: 'string'),
        new OA\Property(property: 'to', type: 'number'),
        new OA\Property(property: 'total', type: 'number'),
    ]
)]
#[OA\Schema(
    schema: 'LoginResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(property: 'apiToken', type: 'string'),
                new OA\Property(property: 'tokenType', type: 'string', example: 'Bearer'),
                new OA\Property(property: 'expiresAt', type: 'number'),
                new OA\Property(property: 'user', ref: '#/components/schemas/BaseInfoResponse')
            ],
            type: 'object'
        )
    ]
)]

#[OA\Schema(
    schema: 'VerifyEmailResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'number', example: 200),
        new OA\Property(property: 'result_detail', type: 'string')
    ]
)]

class ResponseSchema {}
