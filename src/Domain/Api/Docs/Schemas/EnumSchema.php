<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'GenderEnum',
    description: 'MALE: 男性, FEMALE: 女性',
    type: 'string',
    enum: ['MALE', 'FEMALE']
)]
#[OA\Schema(
    schema: 'CertificateLevelEnum',
    description: 'N1: N1, N2: N2, N3: N3, N4: N4, N5: N5, SAME_N1: 同等N1, SAME_N2: 同等N2, SAME_N3: 同等N3, SAME_N4: 同等N4, SAME_N5: 同等N5',
    type: 'string',
    enum: ['N1', 'N2', 'N3', 'N4', 'N5', 'SAME_N1', 'SAME_N2', 'SAME_N3', 'SAME_N4', 'SAME_N5']
)]
#[OA\Schema(
    schema: 'DepositTypeEnum',
    description: 'NORMAL: 普通, CURRENT: 当座, FIXED: 定期',
    type: 'string',
    enum: ['NORMAL', 'CURRENT', 'FIXED']
)]
#[OA\Schema(
    schema: 'EmergencyRelationEnum',
    description: 'FAMILY: 家族, RELATIVE: 親戚, FRIEND: 友人, SCHOOL: 学校関係者',
    type: 'string',
    enum: ['FAMILY', 'RELATIVE', 'FRIEND', 'SCHOOL']
)]
#[OA\Schema(
    schema: 'FeeTypeEnum',
    description: 'DAY: 日単位, HOUR: 時間単位',
    type: 'string',
    enum: ['DAY', 'HOUR']
)]
#[OA\Schema(
    schema: 'JobTypeEnum',
    description: 'FULL_TIME: フルタイム, PART_TIME: パートタイム, OTHER: その他',
    type: 'string',
    enum: ['FULL_TIME', 'PART_TIME', 'OTHER']
)]
#[OA\Schema(
    schema: 'PeriodTypeEnum',
    description: 'STUDENT: 学生, FAMILY: 家族, SPECIFIC_ACTIVITY: 特定活動, PERMANENT_RESIDENT: 永住者, SPOUSE_OF_JAPANESE: 日本人の配偶者等, SPOUSE_OF_PERMANENT_RESIDENT: 永住者の配偶者等, LONG_TERM_RESIDENT: 定住者, OTHER: その他',
    type: 'string',
    enum: ['STUDENT', 'FAMILY', 'SPECIFIC_ACTIVITY', 'PERMANENT_RESIDENT', 'SPOUSE_OF_JAPANESE', 'SPOUSE_OF_PERMANENT_RESIDENT', 'LONG_TERM_RESIDENT', 'OTHER']
)]
#[OA\Schema(
    schema: 'RecruitmentTypeEnum',
    description: 'ADMIN: 管理者, AUTO: 自動',
    type: 'string',
    enum: ['ADMIN', 'AUTO']
)]
#[OA\Schema(
    schema: 'UserBankTypeEnum',
    description: 'CASH: 現金, BANK: 銀行',
    type: 'string',
    enum: ['CASH', 'BANK']
)]
class EnumSchema {}
