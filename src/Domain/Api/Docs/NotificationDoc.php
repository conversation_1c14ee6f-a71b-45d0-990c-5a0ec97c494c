<?php

namespace Src\Domain\Api\Docs;

use OpenApi\Attributes as OA;

#[OA\Tag(
    name: 'Notification',
    description: 'Notification endpoints'
)]
class NotificationDoc
{
    #[OA\Get(
        path: '/announcement',
        summary: 'Get announcement',
        tags: ['Notification'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/AnnouncementResponse')
            )
        ]
    )]
    public function AnnouncementDoc() {}

    #[OA\Get(
        path: '/notification',
        summary: 'Get notification User',
        security: [['passport' => []]],
        tags: ['Notification'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/ListNotificationResponse')
            )
        ]
    )]
    public function getNotificationUser() {}

    #[OA\Get(
        path: '/notification/{id}',
        summary: 'Get detail notification User',
        security: [['passport' => []]],
        tags: ['Notification'],
        parameters: [
            new OA\Parameter(
                name: 'id',
                description: 'Notification id',
                in: 'path',
                required: true,
                schema: new OA\Schema(
                    type: 'number',
                )
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(ref: '#/components/schemas/NotificationUserResponse')
            )
        ],
    )]
    public function getDetailNotificationUser() {}
}
