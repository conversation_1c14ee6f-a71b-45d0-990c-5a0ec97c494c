<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Services\FaqService;
use Src\Enums\ResultCode;

/**
 * Class FAQController
 * @package Src\Domain\Api\Controllers
 */
class FAQController extends Controller
{
    protected FaqService $service;

    public function __construct(FaqService $service)
    {
        $this->service = $service;
    }

    /**
     * Get a list of FAQ
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $result = $this->service->fetchAll();
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('failed_to_fetch_faq'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Get a detail of FAQ
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $result = $this->service->findOrFail($id);
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('failed_to_fetch_faq'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

}
