<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Services\PolicyService;
use Src\Enums\ResultCode;

/**
 * Class PolicyController
 * @package Src\Domain\Api\Controllers
 */
class PolicyController extends Controller
{
    protected PolicyService $service;

    public function __construct(PolicyService $service)
    {
        $this->service = $service;
    }

    /**
     * Get a list of Policy
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $result = $this->service->fetchAll();
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('failed_to_fetch_policy'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Get a Detail of Policy
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $result = $this->service->findOrFail($id);
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('failed_to_fetch_policy'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

}
