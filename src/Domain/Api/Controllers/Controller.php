<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Src\Exception\AuthenticationException;
use Auth;

/**
 * Class Controller
 * @package Src\Domain\Api\Controllers
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * author
     *
     * @throws AuthenticationException
     */
    protected function author()
    {
        $author = Auth::guard('api')->user();
        if (null === $author) {
            throw new AuthenticationException('Unauthorized.');
        }
        return $author;
    }
}
