<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Services\NotificationService;
use Src\Enums\ResultCode;

/**
 * Class NotificationController
 * @package Src\Domain\Api\Controllers
 */
class NotificationController extends Controller
{
    /**
     * Announcement Index
     * @param NotificationService $service
     * @return \Illuminate\Http\JsonResponse
     */
    public function announcementIndex(NotificationService $service): JsonResponse
    {
        $result = $service->fetchAnnouncement();

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Index
     * @param NotificationService $service
     * @return JsonResponse
     * @throws \Src\Exception\AuthenticationException
     */
    public function index(NotificationService $service)
    {
        $author = $this->author();
        $result = $service->fetchAll($author->id);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Show notification user
     * @param int $id
     * @param NotificationService $service
     * @return JsonResponse
     * @throws \Src\Exception\AuthenticationException
     */
    public function show(int $id, NotificationService $service)
    {
        $author = $this->author();
        $result = $service->findOrFail($author->id, $id);

        return json_response(ResultCode::SUCCESS, $result);
    }
}
