<?php

namespace Src\Domain\Api\Controllers;

use App\Exceptions\APIRuntimeException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Services\EmailVerificationService;
use Src\Enums\ResultCode;

/**
 * class EmailVerificationController
 */
class EmailVerificationController extends Controller
{
    /**
     *
     * @param Request $request
     * @param EmailVerificationService $service
     * @return JsonResponse
     */
    public function verifyEmail(Request $request, EmailVerificationService $service): JsonResponse
    {
        if (!$service->verificationEmail($request->token)) {
            return json_response(ResultCode::ERROR_INPUT, __('flash.verify_email.failed'));
        }

        return json_response(ResultCode::SUCCESS, __('flash.verify_email.succeeded'));
    }
}
