<?php

namespace Src\Domain\Api\Controllers;

use App\Exceptions\APIRuntimeException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Services\MasterService;
use Src\Enums\EmergencyRelation;
use Src\Enums\ResultCode;

/**
 * Class MasterController
 * @package Src\Domain\Api\Controllers
 */
class MasterController extends Controller
{
    /**
     * Get address data by zipcode
     * @param Request $request
     * @param MasterService $service
     * @return JsonResponse
     */
    public function getZipcodeData(Request $request, MasterService $service): JsonResponse
    {
        $zip_code = $request->input('zip_code');
        $result = $service->findWithZipcode($zip_code);
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('Get zipcode error!'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * List Emergency Relation
     * @return JsonResponse
     */
    public function getEmergencyRelation(): JsonResponse
    {
        $relations = EmergencyRelation::toApiResponse();
        return json_response(ResultCode::SUCCESS, $relations);
    }

    /**
     * Get to list job categories
     * @param MasterService $service
     * @return JsonResponse
     */
    public function getJobCategories(MasterService $service): JsonResponse
    {
        $result = $service->fetchJobCategories();

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * List countries
     * @return JsonResponse
     */
    public function getCountries(): JsonResponse
    {
        $countries = list_countries();
        $formatted = array_map(function ($code, $name) {
            return [
                'label' => $name,
                'value' => $code
            ];
        }, array_keys($countries), array_values($countries));

        return json_response(ResultCode::SUCCESS, $formatted);
    }

    public function getBanks(MasterService $service)
    {
        $result = $service->listBanks();
        return json_response(ResultCode::SUCCESS, $result);
    }

    public function getBankBranches(string $code, MasterService $service)
    {
        $result = $service->listBankBranches($code);
        return json_response(ResultCode::SUCCESS, $result);
    }
}
