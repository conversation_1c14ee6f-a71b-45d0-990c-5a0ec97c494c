<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Models\Job\JobSearchForm;
use Src\Domain\Api\Requests\Job\JobApplicationRequest;
use Src\Domain\Api\Services\JobApplicationService;
use Src\Domain\Api\Requests\Job\CancelJobApplyRequest;
use Src\Domain\Api\Services\JobService;
use Src\Enums\ResultCode;
use Src\Exception\AuthenticationException;
use Exception;
use Throwable;

/**
 * Class JobController
 * @package Src\Domain\Api\Controllers
 */
class JobController extends Controller
{
    protected JobService $service;

    public function __construct(JobService $service)
    {
        $this->service = $service;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $form = new JobSearchForm($request->all());
        $result = $this->service->fetchAll($form);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Show
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $result = $this->service->findOrFail($id);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * List Job Apply
     *
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function listJobUserApplied(): JsonResponse
    {
        $result = $this->service->listJobUserApplied($this->author());

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Cancel Job Applied
     *
     * @param CancelJobApplyRequest $request
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function cancelJobApplied(CancelJobApplyRequest $request): JsonResponse
    {

        $result = $this->service->cancelJobApplied($this->author(), $request->job_id);

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('api_errors.job.cancel_job_fails'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * @param int $id
     * @param JobApplicationRequest $request
     * @return JsonResponse
     * @throws Exception|Throwable
     */
    public function applyToJob(JobApplicationRequest $request, int $id): JsonResponse
    {
        $result = $this->service->apply($request->validatedForm(), $this->author(), $id);

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('api_errors.job.apply_failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * @param int $id
     * @return JsonResponse
     * @throws Exception
     */
    public function addToFavorites(int $id): JsonResponse
    {
        $author = $this->author();
        $result = $this->service->favorite($author->id, $id);

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('api_errors.job.already_favorite'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * @param int $id
     * @param JobService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function removeToFavorite(JobService $service, int $id): JsonResponse
    {
        $author = $this->author();

        $result = $service->remove($author->id, $id);

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('api_errors.job.favorite_deletion_failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }
}
