<?php

namespace Src\Domain\Api\Controllers;

use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Requests\Auth\UpdateProfileRequest;
use Src\Domain\Api\Services\UserService;
use Src\Enums\ResultCode;
use Src\Exception\AuthenticationException;

class UserController extends Controller
{
    /**
     * Get current authenticated user.
     *
     * @param UserService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function getProfile(UserService $service): JsonResponse
    {
        $author = $this->author();
        $result = $service->getCurrentUser($author);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Get current authenticated user.
     *
     * @param UpdateProfileRequest $request
     * @param UserService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function updateProfile(UpdateProfileRequest $request,UserService $service): JsonResponse
    {
        $author = $this->author();
        $result = $service->updateProfile($request->validatedForm(), $author->id);

        return json_response(ResultCode::SUCCESS, $result);
    }
}
