<?php

namespace Src\Domain\Api\Controllers;

use App\Exceptions\APIRuntimeException;
use Exception;
use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Requests\Auth\CheckExistEmailRequest;
use Src\Domain\Api\Requests\Auth\LoginRequest;
use Src\Domain\Api\Requests\Auth\SignUpRequest;
use Src\Domain\Api\Requests\Password\ChangePasswordRequest;
use Src\Domain\Api\Requests\Password\ForgotPasswordRequest;
use Src\Domain\Api\Requests\Password\ResetPasswordRequest;
use Src\Domain\Api\Services\AuthService;
use Src\Domain\Api\Services\PasswordService;
use Src\Enums\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * class AuthController
 * @package Src\Domain\Api\Controllers
 */
class AuthController extends Controller
{
    /**
     * Login
     * @param LoginRequest $request
     * @param AuthService $service
     * @return JsonResponse
     */
    public function login(LoginRequest $request, AuthService $service): JsonResponse
    {
        $result = $service->login($request->validatedForm());
        if (!$result) {
            throw new ApiRuntimeException('api_errors.auth.login_failed');
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function logout(): JsonResponse
    {
        $author = $this->author();
        $author->token()->revoke();
        $result_detail = [
            'message' => 'Logged out successfully.',
        ];

        return json_response(ResultCode::SUCCESS, $result_detail);
    }

    /**
     * @param SignUpRequest $request
     * @param AuthService $service
     * @return JsonResponse
     */
    public function signUp(SignUpRequest $request, AuthService $service): JsonResponse
    {
        $result = $service->signUp($request->validatedForm());

        if (!$result) {
            return json_response(ResultCode::ERROR_INPUT, null, __('flash.sign_up.failed'));
        }
        return json_response(ResultCode::SUCCESS, __('flash.sign_up.succeeded'));
    }

    /**
     * @param ChangePasswordRequest $request
     * @param PasswordService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function changePassword(ChangePasswordRequest $request, PasswordService $service): JsonResponse
    {
        $result_password = $service->changePasswordServices($request->validatedForm());

        if (!$result_password) {
            return json_response(ResultCode::ERROR, null, __('Password is incorrect!'));
        }
        return json_response(ResultCode::SUCCESS, $result_password);
    }

    /**
     * @param ForgotPasswordRequest $request
     * @param PasswordService $service
     * @return JsonResponse
     */
    public function forgotPassword(ForgotPasswordRequest $request, PasswordService $service): JsonResponse
    {
        $result_password = $service->forgotPasswordServices($request->validatedForm());

        if (!$result_password) {
            return json_response(ResultCode::ERROR, null, __('Failed when reset password!'));
        }
        return json_response(ResultCode::SUCCESS, $result_password);
    }

    /**
     * @param ResetPasswordRequest $request
     * @param PasswordService $service
     * @return JsonResponse
     */
    public function resetPassword(ResetPasswordRequest $request, PasswordService $service): JsonResponse
    {
        $result_password = $service->resetPasswordServices($request->validatedForm());

        if (!$result_password) {
            return json_response(ResultCode::ERROR, null, __('Password is incorrect!'));
        }
        return json_response(ResultCode::SUCCESS, $result_password);
    }

    public function checkExistEmail(CheckExistEmailRequest $request, AuthService $service): JsonResponse
    {
        $result = $service->checkExistEmail($request->email);

        return json_response(ResultCode::SUCCESS, $result);
    }
}
