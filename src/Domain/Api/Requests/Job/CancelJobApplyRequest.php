<?php

namespace Src\Domain\Api\Requests\Job;

use Src\Domain\Api\Requests\FormRequest;

/**
 * Class LoginRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class CancelJobApplyRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'job_id' => [
                'required',
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [

        ];
    }

    public function validatedForm()
    {
        //
    }
}
