<?php

namespace Src\Domain\Api\Requests\Job;

use Src\Domain\Api\Models\JobApplication\JobApplicationForm;
use Src\Domain\Api\Models\Password\ForgotPasswordForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class JobApplicationRequest
 * @package Src\Domain\Api\Requests\Job
 */
class JobApplicationRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'resume_id' => [
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
        ];
    }

    /**
     * Messages
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'resume_id.image' => __('validation.image', ['attribute' => 'resume']),
            'resume_id.mimes' => __('validation.mimes', ['attribute' => 'resume']),
            'resume_id.max' => __('validation.max', ['attribute' => 'resume']),
        ];
    }

    /**
     * @return JobApplicationForm
     */
    public function validatedForm(): JobApplicationForm
    {
        return new JobApplicationForm($this->validated());
    }
}
