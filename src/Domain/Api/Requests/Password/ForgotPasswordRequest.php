<?php

namespace Src\Domain\Api\Requests\Password;

use Src\Domain\Api\Models\Password\ForgotPasswordForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class ForgotPasswordRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class ForgotPasswordRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                'string'
            ],
            'birthday' => [
                'required',
                'digits:8',
            ]
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'email' => 'email',
            'birthday' => 'birthday'
        ];
    }

    /**
     * Messages
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'email.required' => __('validation.required', ['attribute' => 'email']),
            'email.email' => __('validation.email', ['attribute' => 'email']),
            'email.string' => __('validation.string', ['attribute' => 'email']),
            'birthday.required' => __('validation.required', ['attribute' => 'birthday']),
            'birthday.digits' => __('validation.digits', ['attribute' => 'birthday']),
        ];
    }

    /**
     * @return ForgotPasswordForm
     */
    public function validatedForm(): ForgotPasswordForm
    {
        return new ForgotPasswordForm($this->validated());
    }
}
