<?php

namespace Src\Domain\Api\Requests\Password;

use Src\Domain\Api\Models\Password\ResetPasswordForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class ResetPasswordRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class ResetPasswordRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'token' => [
                'required',
                'string',
            ],
            'new_password' => [
                'required',
                'string',
                'min:8',
                'max:100',
                'different:old_password',
            ],
            'confirm_password' => [
                'required',
                'string',
                'same:new_password',
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'new_password' => 'New Password',
            'confirm_password' => 'Confirm Password',
        ];
    }

    /**
     * Messages
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'new_password.different' => 'The new password must be different from the old password.',
            'confirm_password.same' => 'The new password confirmation does not match.'
        ];
    }

    /**
     * @return ResetPasswordForm
     */
    public function validatedForm(): ResetPasswordForm
    {
        return new ResetPasswordForm($this->validated());
    }
}
