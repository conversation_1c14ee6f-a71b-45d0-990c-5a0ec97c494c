<?php

namespace Src\Domain\Api\Requests\Auth;

use Src\Domain\Api\Models\User\UserForm;

/**
 * Class UpdateProfileRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class UpdateProfileRequest extends BaseRequestUser
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'password' => [
                'nullable',
                'string',
                'min:8',
                'max:100',
            ],
            'avatar_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'health_certificate_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'front_card_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'back_card_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'front_identification_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'back_identification_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
            'passport_image_id' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,heic,heif',
                'max:5120',
            ],
        ]);
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [

        ];
    }

    /**
     * @return UserForm
     */
    public function validatedForm(): UserForm
    {
        return new UserForm($this->validated());
    }
}
