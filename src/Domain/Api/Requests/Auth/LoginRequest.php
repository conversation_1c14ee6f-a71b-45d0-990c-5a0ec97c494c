<?php

namespace Src\Domain\Api\Requests\Auth;

use Src\Domain\Api\Models\Auth\LoginForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class LoginRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class LoginRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'string',
                'max:100'
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'max:45',
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [

        ];
    }

    /**
     * @return LoginForm
     */
    public function validatedForm(): LoginForm
    {
        return new LoginForm($this->validated());
    }
}
