<?php

namespace Src\Domain\Admin\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LanguageController extends Controller
{
    public function change(Request $request)
    {
        $locale = $request->input('locale');

        if (!in_array($locale, ['en', 'id', 'jp', 'mm', 'ne', 'vi'])) {
            return response()->json(['error' => 'Unsupported language'], 400);
        }

        session()->put('locale', $locale);

        return response()->json(['success' => true]);
    }

    public function current()
    {
        return response()->json([
            'locale' => session('locale', 'jp')
        ]);
    }
}
