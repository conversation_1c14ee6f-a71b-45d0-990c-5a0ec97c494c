<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Src\Domain\Admin\Services\JobApplicationService;
use Src\Domain\Admin\Services\JobService;

class JobApplicationController extends Controller
{
    public function index(Request $request, JobApplicationService $service)
    {
        $date= $request->query('date');
        $is_month = $request->query('is_month');
        $jobs = $service->fetchAll($date, $is_month);
        return Inertia::render('JobApplication/IndexPage', ['jobs' => $jobs, 'date' => $date]);
    }

    public function show(int $job_id, JobApplicationService $service, JobService $job_service)
    {
        $job = $job_service->findOrFail($job_id);
        $job_appliers = $service->fetchApplier($job_id);
        return Inertia::render('JobApplication/DetailPage', ['job' => $job, 'appliers' => $job_appliers]);
    }

    public function approval()
    {
        //
    }
}
