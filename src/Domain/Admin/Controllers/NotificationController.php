<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Notification\NotificationSearchForm;
use Src\Domain\Admin\Requests\Notification\CreateRequest;
use Src\Domain\Admin\Requests\Notification\UpdateRequest;
use Src\Domain\Admin\Services\NotificationService;

class NotificationController extends Controller
{
    protected NotificationService $service;

    public function __construct(NotificationService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request): Response
    {
        $form = new NotificationSearchForm($request->all());
        $notifications = $this->service->fetchAll($form);
        return Inertia::render('Notification/IndexPage', ['notifications' => $notifications]);
    }

    public function create(): Response
    {
        return Inertia::render('Notification/CreatePage');
    }

    public function store(CreateRequest $request): RedirectResponse
    {
        if ($this->service->store($request->validatedForm())) {
            return to_route('admin.notification.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    public function show(int $notification_id): Response
    {
        $notification = $this->service->findOrFail($notification_id);
        return Inertia::render('Notification/DetailPage', ['notification' => $notification]);
    }

    public function edit(int $notification_id): Response
    {
        $notification = $this->service->findOrFail($notification_id);
        return Inertia::render('Notification/EditPage', ['notification' => $notification]);
    }

    public function update(int $notification_id, UpdateRequest $request): RedirectResponse
    {
        $result = $this->service->update($notification_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.notification.show', $notification_id)->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    public function delete(int $notification_id): RedirectResponse
    {
        $result = $this->service->delete($notification_id);
        if ($result) {
            return to_route('admin.notification.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }
}
