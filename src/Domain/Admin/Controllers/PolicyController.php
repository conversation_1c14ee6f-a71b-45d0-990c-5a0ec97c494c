<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Policy\PolicySearchForm;
use Src\Domain\Admin\Requests\Policy\CreateRequest;
use Src\Domain\Admin\Requests\Policy\UpdateRequest;
use Src\Domain\Admin\Services\PolicyService;

/**
 * Class PolicyController
 * @package Src\Domain\Admin\Controllers
 */
class PolicyController extends Controller
{
    /**
     * @var PolicyService
     */
    protected PolicyService $service;

    /**
     * Constructor to initialize the Policy service.
     *
     * @param PolicyService $service
     */
    public function __construct(PolicyService $service)
    {
        $this->service = $service;
    }

    /**
     * Index
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $form = new PolicySearchForm($request->all());
        $policies = $this->service->fetchAll($form);
        return Inertia::render('Policy/IndexPage', ['policies' => $policies]);
    }

    /**
     * Create
     *
     * @return Response
     */
    public function create(): Response
    {
        return Inertia::render('Policy/CreatePage');
    }

    /**
     * Store
     *
     * @param CreateRequest $request
     * @return RedirectResponse
     */
    public function store(CreateRequest $request): RedirectResponse
    {
        if ($this->service->store($request->validatedForm())) {
            return to_route('admin.policy.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Show
     *
     * @param int $policy_id
     * @return Response
     */
    public function show(int $policy_id): Response
    {
        $policy = $this->service->findOrFail($policy_id);
        return Inertia::render('Policy/DetailPage', ['policy' => $policy]);
    }

    /**
     * Edit
     *
     * @param int $policy_id
     * @return Response
     */
    public function edit(int $policy_id): Response
    {
        $policy = $this->service->findOrFail($policy_id);
        return Inertia::render('Policy/EditPage', ['policy' => $policy]);
    }

    /**
     * Update
     *
     * @param int $policy_id
     * @param UpdateRequest $request
     * @return RedirectResponse
     */
    public function update(int $policy_id, UpdateRequest $request): RedirectResponse
    {
        $result = $this->service->update($policy_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.policy.show', $policy_id)->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Delete
     *
     * @param int $policy_id
     * @return RedirectResponse
     */
    public function delete(int $policy_id): RedirectResponse
    {
        $result = $this->service->delete($policy_id);
        if ($result) {
            return to_route('admin.policy.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }
}
