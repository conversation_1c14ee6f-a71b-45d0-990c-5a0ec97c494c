<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Account\AccountSearchForm;
use Src\Domain\Admin\Requests\Account\CreateRequest;
use Src\Domain\Admin\Requests\Account\EditRequest;
use Src\Domain\Admin\Services\AccountService;

/**
 * Class AccountController
 * @package Src\Domain\Admin\Controllers
 */
class AccountController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param AccountService $service
     * @return Response
     */
    public function index(Request $request, AccountService $service): Response
    {
        $search = new AccountSearchForm($request->all());
        $accounts = $service->fetchAll($search);
        return Inertia::render('Account/IndexPage', ['accounts' => $accounts]);
    }

    /**
     * show
     *
     * @param int $account_id
     * @param AccountService $service
     * @return Response
     */
    public function show(int $account_id, AccountService $service): Response
    {
        $account = $service->findOrFail($account_id);

        return Inertia::render('Account/DetailPage', ['account' => $account]);
    }

    /**
     * create
     *
     * @return Response
     */
    public function create(): Response
    {
        return Inertia::render('Account/CreatePage');
    }

    /**
     * store
     *
     * @param CreateRequest $request
     * @param AccountService $service
     * @return RedirectResponse
     */
    public function store(CreateRequest $request, AccountService $service): RedirectResponse
    {
        if ($service->store($request->validatedForm())) {
            return to_route('admin.account.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Edit Account
     *
     * @param int $account_id
     * @param AccountService $service
     * @return Response
     */
    public function edit(int $account_id, AccountService $service): Response
    {
        $account = $service->findOrFail($account_id);
        return Inertia::render('Account/EditPage', ['account' => $account]);
    }

    /**
     * Update the specified account.
     *
     * @param int $account_id
     * @param EditRequest $request
     * @param AccountService $service
     * @return RedirectResponse
     */
    public function update(int $account_id, EditRequest $request, AccountService $service): RedirectResponse
    {
        $result = $service->update($account_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.account.index')->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Remove the specified account.
     *
     * @param int $account_id
     * @param AccountService $service
     * @return RedirectResponse
     */
    public function delete(int $account_id, AccountService $service): RedirectResponse
    {
        $result = $service->delete($account_id);
        if ($result) {
            return to_route('admin.account.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }
}
