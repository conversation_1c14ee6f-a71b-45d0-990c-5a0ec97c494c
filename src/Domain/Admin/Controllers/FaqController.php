<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Faq\FaqSearchForm;
use Src\Domain\Admin\Requests\Faq\CreateRequest;
use Src\Domain\Admin\Requests\Faq\UpdateRequest;
use Src\Domain\Admin\Services\FaqService;

/**
 * FaqController
 * @package Src\Domain\Admin\Controllers
 */
class FaqController extends Controller
{
    /**
     * @var FaqService
     */
    protected FaqService $service;

    /**
     * Constructor to initialize the FAQ service.
     *
     * @param FaqService $service
     */
    public function __construct(FaqService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a list of FAQs.
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $form = new FaqSearchForm($request->all());
        $faqs = $this->service->fetchAll($form);
        return Inertia::render('Faq/IndexPage', ['faqs' => $faqs]);
    }

    /**
     * Show the form for creating a new FAQ.
     *
     * @return Response
     */
    public function create(): Response
    {
        return Inertia::render('Faq/CreatePage');
    }

    /**
     * Store FAQ
     *
     * @param CreateRequest $request
     * @return RedirectResponse
     */
    public function store(CreateRequest $request): RedirectResponse
    {
        if ($this->service->store($request->validatedForm())) {
            return to_route('admin.faq.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Display the detail FAQ.
     *
     * @param int $faq_id
     * @return Response
     */
    public function show(int $faq_id): Response
    {
        $faq = $this->service->findOrFail($faq_id);
        return Inertia::render('Faq/DetailPage', ['faq' => $faq]);
    }

    /**
     * Show the form for editing an existing FAQ.
     *
     * @param int $faq_id
     * @return Response
     */
    public function edit(int $faq_id): Response
    {
        $faq = $this->service->findOrFail($faq_id);
        return Inertia::render('Faq/EditPage', ['faq' => $faq]);
    }

    /**
     * Update an existing FAQ
     *
     * @param int $faq_id
     * @param UpdateRequest $request
     * @return RedirectResponse
     */
    public function update(int $faq_id, UpdateRequest $request): RedirectResponse
    {
        $result = $this->service->update($faq_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.faq.show', $faq_id)->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Delete an existing FAQ
     *
     * @param int $faq_id
     * @return RedirectResponse
     */
    public function delete(int $faq_id): RedirectResponse
    {
        $result = $this->service->delete($faq_id);
        if ($result) {
            return to_route('admin.faq.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }
}
