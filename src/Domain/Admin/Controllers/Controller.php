<?php

namespace Src\Domain\Admin\Controllers;

use Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Src\Exception\AuthenticationException;

/**
 * Class Controller
 * @package Src\Domain\Admin\Controller
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs;

    /**
     * @throws AuthenticationException
     */
    protected function author()
    {
        $author = Auth::guard('admin')->user();
        if (null === $author) {
            throw new AuthenticationException('Unauthorized.');
        }
        return $author;
    }
}
