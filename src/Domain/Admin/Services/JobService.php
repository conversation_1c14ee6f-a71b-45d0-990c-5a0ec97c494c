<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Job;
use App\Eloquent\JobCategory;
use App\Eloquent\JobImage;
use App\Eloquent\StorageFile;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Admin\Models\Job\JobDetail;
use Src\Domain\Admin\Models\Job\JobForm;
use Src\Domain\Admin\Models\Job\JobSearchForm;
use Src\Enums\FileDiv;
use Src\Traits\StorageFileTrait;
use Throwable;

/**
 * Class JobService
 * @package Src\Domain\Admin\Services
 */
class JobService
{
    use StorageFileTrait;

    /**
     * Fetch all jobs with pagination
     *
     * @param JobSearchForm $search_form
     * @return array
     */
    public function fetchAll(JobSearchForm $search_form)
    {
        $query = $this->jobQuery()->with(['category', 'thumbnail'])->orderBy('id', 'desc');
        $query = $search_form->searchConditions($query);

        $paginator = $query->paginate(PER_PAGE);
        $paginator->getCollection()->transform(function ($job) {
            return (new JobDetail($job))->toListComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find job by id
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        /** @var Job $job */
        $job = $this->jobQuery()->with(['category', 'thumbnail', 'images'])->findOrFail($id);

        return (new JobDetail($job))->toComponent();
    }

    /**
     * Store new job
     *
     * @param JobForm $form
     * @return bool
     */
    public function store(JobForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                $jobAttributes = $form->createAttributes();

                if ($form->getThumbnail()) {
                    $thumbnailId = $this->createStorageFile(
                        $form->getThumbnail(),
                        FileDiv::IMAGE,
                        'jobs/thumbnails'
                    );
                    $jobAttributes['thumbnail_id'] = $thumbnailId;
                }

                /** @var Job $job */
                $job = $this->jobQuery()->createOrThrow($jobAttributes);

                // Handle images upload
                if ($form->getImages() && is_array($form->getImages())) {
                    foreach ($form->getImages() as $image) {
                        if ($image instanceof \Illuminate\Http\UploadedFile) {
                            $imageId = $this->createStorageFile(
                                $image,
                                FileDiv::IMAGE,
                                'jobs/images'
                            );

                            $job->images()->create([
                                'image_id' => $imageId,
                            ]);
                        }
                    }
                }

                logger_info('Successfully stored Job', ['id' => $job->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to store Job: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Update job
     *
     * @param int $id
     * @param JobForm $form
     * @return bool
     */
    public function update(int $id, JobForm $form): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id, $form) {
                /** @var Job $job */
                $job = $this->jobQuery()->with(['images'])->findOrFail($id);

                $jobAttributes = $form->editAttributes();

                // Handle thumbnail update
                if ($form->getThumbnail()) {
                    $thumbnailId = $this->createStorageFile(
                        $form->getThumbnail(),
                        FileDiv::IMAGE,
                        'jobs/thumbnails'
                    );
                    $jobAttributes['thumbnail_id'] = $thumbnailId;
                }

                $job->updateOrThrow($jobAttributes);

                // Handle image deletions
                if (!empty($form->getDeletedImageIds())) {
                    $job->images()->whereIn('id', $form->getDeletedImageIds())->delete();
                }

                // Handle new images upload
                if ($form->getImages() && is_array($form->getImages())) {
                    foreach ($form->getImages() as $image) {
                        if ($image instanceof \Illuminate\Http\UploadedFile) {
                            $imageId = $this->createStorageFile(
                                $image,
                                FileDiv::IMAGE,
                                'jobs/images'
                            );
                            $job->images()->create([
                                'image_id' => $imageId,
                            ]);
                        }
                    }
                }

                logger()->info('Successfully updated Job', ['id' => $job->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update Job', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Delete job
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id) {
                /** @var Job $job */
                $job = $this->jobQuery()->findOrFail($id);
                $job->delete();
                logger()->info('Successfully deleted Job', ['id' => $id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to delete Job', ['id' => $id], $e);
        }
        return $result;
    }

    public function duplicate(int $job_id)
    {
        /** @var Job $job */
        $job = $this->jobQuery()->findOrFail($job_id);

        try {
            return DB::transaction(function () use ($job) {
                $job_copy = new Job();
                foreach ($job->getAttributes() as $key => $value) {
                    if (in_array($key, $job_copy->getFillable()) && !in_array($key, ['id', 'created_at', 'updated_at'])) {
                        $job_copy->$key = $value;
                    }
                }
                $job_copy->save();
                if ($job->images->isNotEmpty()) {
                    $job_copy->images()->createMany($job->images->map(function ($image) {
                        return [
                            'image_id' => $image->image_id,
                        ];
                    })->toArray());
                }

                logger()->info('Successfully duplicated Job', ['id' => $job_copy->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to duplicate Job', ['id' => $job_id], $e);
            return false;
        }
    }

    /**
     * Get job query
     *
     * @return Job|Builder
     */
    protected function jobQuery(): Job|Builder
    {
        return Job::queryModel();
    }
}
