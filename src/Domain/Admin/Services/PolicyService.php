<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Policy;
use Src\Domain\Admin\Models\Policy\PolicyDetail;
use Src\Domain\Admin\Models\Policy\PolicyForm;
use Src\Domain\Admin\Models\Policy\PolicySearchForm;
use DB;

class PolicyService
{
    /**
     * Fetch all Policy policies
     *
     * @param PolicySearchForm $form
     */
    public function fetchAll(PolicySearchForm $form)
    {
        $query = $this->policyQuery()->orderBy('id', 'desc');

        if ($form->getTitle()) {
            $query->where('title', 'like', '%' . $form->getTitle() . '%');
        }

        if ($form->getIsPublic() !== null) {
            $query->where('is_public', $form->getIsPublic());
        }

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($policy) {
            return (new PolicyDetail($policy))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find Policy by id
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        /** @var Policy $policy */
        $policy = $this->policyQuery()->findOrFail($id);

        return (new PolicyDetail($policy))->toComponent();
    }

    /**
     * Store new Policy
     *
     * @param PolicyForm $form
     * @return bool
     */
    public function store(PolicyForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Policy $policy */
                $policy = $this->policyQuery()->createOrThrow($form->createAttributes());
                logger_info('Successfully stored Policy', ['id' => $policy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger_error('Failed to store Policy: ', $form->toArray(), $e);
        }
        return $result;
    }

    /**
     * Update Policy
     *
     * @param int $id
     * @param PolicyForm $form
     * @return bool
     */
    public function update(int $id, PolicyForm $form): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id, $form) {
                /** @var Policy $policy */
                $policy = $this->policyQuery()->findOrFail($id);
                $policy->updateOrThrow($form->editAttributes());
                logger()->info('Successfully updated Policy', ['id' => $policy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('Failed to update Policy', $form->toArray());
        }
        return $result;
    }

    /**
     * Delete Policy
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id) {
                /** @var Policy $policy */
                $policy = $this->policyQuery()->findOrFail($id);
                $policy->delete();
                logger()->info('Successfully deleted Policy', ['id' => $policy->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('Failed to delete Policy', ['id' => $id]);
        }
        return $result;
    }

    private function policyQuery()
    {
        return Policy::query()->getModel();
    }
}
