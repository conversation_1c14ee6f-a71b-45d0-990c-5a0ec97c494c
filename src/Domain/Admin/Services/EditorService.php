<?php

namespace Src\Domain\Admin\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Src\Domain\Admin\Models\TextEditor\UploadForm;
use Src\Traits\StorageFileTrait;
use Src\Traits\Utils\FileUpload\FileUploadable;

class EditorService
{
    use FileUploadable, StorageFileTrait;

    /**
     * Upload image temporarily for CKEditor
     *
     * @param UploadForm $form
     * @return array|null
     */
    public function uploadImageTemporary(UploadForm $form): ?array
    {
        try {
            $file = $form->getUpload();

            if (!$file instanceof UploadedFile) {
                return null;
            }

            $tempResult = $this->putFileToTmpStorage($file, 'editor/temp');

            if (!$tempResult) {
                return null;
            }

            return [
                'url' => $tempResult['filePath'],
                'uploaded' => true,
                'fileName' => $tempResult['originFileName'],
                'tempPath' => $tempResult['filePath']
            ];

        } catch (\Exception $e) {
            logger()->error('Failed to upload temporary image for CKEditor', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName() ?? 'unknown'
            ]);

            return null;
        }
    }

    /**
     * Move images from temporary to permanent storage
     *
     * @param string $content
     * @return string
     */
    public function moveTemporaryImagesToPermanent(string $content): string
    {
        try {
            // Find all temporary image urls
            preg_match_all('/(?:https?:\/\/[^\/]+)?\/storage\/editor\/temp\/[^"\'>\s]+/', $content, $matches);

            if (empty($matches[0])) {
                return $content;
            }
            $tempUrls = $matches[0];
            foreach ($tempUrls as $tempUrl) {
                $tempPath = preg_replace('/^(?:https?:\/\/[^\/]+)?\/storage\//', '', $tempUrl);
                if (Storage::disk('public')->exists($tempPath)) {
                    $fileName = basename($tempPath);
                    $permanentPath = 'editor/images/' . $fileName;

                    $fileContent = Storage::disk('public')->get($tempPath);
                    $mimeType = Storage::disk('public')->mimeType($tempPath);
                    Storage::put($permanentPath, $fileContent, [
                        'ContentType' => $mimeType,
                        'visibility' => 'public'
                    ]);
                    Storage::disk('public')->delete($tempPath);

                    $permanentUrl = Storage::url($permanentPath);
                    $content = str_replace($tempUrl, $permanentUrl, $content);

                    logger()->info('Moved temporary image to S3 storage', [
                        'temp_path' => $tempPath,
                        'permanent_path' => $permanentPath,
                        'permanent_url' => $permanentUrl,
                    ]);
                }
            }

            return $content;

        } catch (\Exception $e) {
            logger()->error('Failed to move temporary images to permanent storage', [
                'error' => $e->getMessage()
            ]);

            return $content;
        }
    }
}
