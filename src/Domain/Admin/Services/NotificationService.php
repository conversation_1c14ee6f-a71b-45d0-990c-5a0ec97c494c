<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Notification;
use Src\Domain\Admin\Models\Notification\NotificationDetail;
use Src\Domain\Admin\Models\Notification\NotificationForm;
use Src\Domain\Admin\Models\Notification\NotificationSearchForm;
use DB;
use Src\Enums\NotificationType;

class NotificationService
{
    /**
     * Fetch all notifications
     *
     * @param NotificationSearchForm $form
     * @return array
     */
    public function fetchAll(NotificationSearchForm $form): array
    {
        $query = $this->notificationQuery()
            ->where('type', NotificationType::PUBLIC)
            ->orderBy('id', 'desc');

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($notification) {
            return (new NotificationDetail($notification))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find notification by id
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        /** @var Notification $notification */
        $notification = $this->notificationQuery()->findOrFail($id);

        return (new NotificationDetail($notification))->toComponent();
    }

    /**
     * Store new notification
     *
     * @param NotificationForm $form
     * @return bool
     */
    public function store(NotificationForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->createOrThrow($form->createAttributes());
                logger_info('Successfully stored notification', ['id' => $notification->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to store notification: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Update notification
     *
     * @param int $id
     * @param NotificationForm $form
     * @return bool
     */
    public function update(int $id, NotificationForm $form): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id, $form) {
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->findOrFail($id);
                $notification->updateOrThrow($form->editAttributes());
                logger_info('Successfully updated notification', ['id' => $notification->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to update notification: ', ['notification_id' => $id, 'form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Delete notification
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id) {
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->findOrFail($id);
                $notification->delete();
                logger_info('Successfully deleted notification', ['id' => $notification->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to delete notification: ', ['notification_id' => $id], $e);
        }
        return $result;
    }

    private function notificationQuery()
    {
        return Notification::queryModel();
    }
}
