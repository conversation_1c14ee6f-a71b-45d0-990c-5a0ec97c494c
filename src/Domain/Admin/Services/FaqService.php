<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Faq;
use Src\Domain\Admin\Models\Faq\FaqDetail;
use Src\Domain\Admin\Models\Faq\FaqForm;
use Src\Domain\Admin\Models\Faq\FaqSearchForm;
use DB;

class FaqService
{
    /**
     * Fetch all FAQs
     *
     * @param FaqSearchForm $form
     * @return array
     */
    public function fetchAll(FaqSearchForm $form): array
    {
        $query = $this->faqQuery()->orderBy('id', 'desc');

        if ($form->getQuestion()) {
            $query->where('question', 'like', '%' . $form->getQuestion() . '%');
        }

        if ($form->getIsPublic() !== null) {
            $query->where('is_public', $form->getIsPublic());
        }

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($faq) {
            return (new FaqDetail($faq))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Find FAQ by id
     *
     * @param int $id
     * @return array
     */
    public function findOrFail(int $id): array
    {
        /** @var Faq $faq */
        $faq = $this->faqQuery()->findOrFail($id);

        return (new FaqDetail($faq))->toComponent();
    }

    /**
     * Store new FAQ
     *
     * @param FaqForm $form
     * @return bool
     */
    public function store(FaqForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Faq $faq */
                $faq = $this->faqQuery()->createOrThrow($form->createAttributes());
                logger_info('Successfully stored FAQ', ['id' => $faq->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to store FAQ: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Update FAQ
     *
     * @param int $id
     * @param FaqForm $form
     * @return bool
     */
    public function update(int $id, FaqForm $form): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id, $form) {
                /** @var Faq $faq */
                $faq = $this->faqQuery()->findOrFail($id);
                $faq->updateOrThrow($form->editAttributes());
                logger_info('Successfully updated FAQ', ['id' => $faq->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to update FAQ: ', ['faq_id' => $id, 'form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * Delete FAQ
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $result = false;

        try {
            $result = DB::transaction(function () use ($id) {
                /** @var Faq $faq */
                $faq = $this->faqQuery()->findOrFail($id);
                $faq->delete();
                logger_info('Successfully deleted FAQ', ['id' => $faq->id]);

                return true;
            });
        } catch (\Throwable $e) {
            logger_error('Failed to delete FAQ: ', ['faq_id' => $id], $e);
        }
        return $result;
    }

    private function faqQuery()
    {
        return Faq::queryModel();
    }
}
