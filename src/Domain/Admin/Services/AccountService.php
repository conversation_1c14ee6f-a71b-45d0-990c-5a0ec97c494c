<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Account;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Admin\Models\Account\AccountDetail;
use Src\Domain\Admin\Models\Account\AccountForm;
use Src\Domain\Admin\Models\Account\AccountSearchForm;
use Src\Domain\Admin\Models\Account\ApprovalForm;
use Src\Domain\Admin\Models\Auth\RegisterForm;
use Throwable;

/**
 * Class AccountService
 * @package Src\Domain\Admin\Services
 */
class AccountService
{
    /**
     * Fetch All Accounts
     */
    public function fetchAll(AccountSearchForm $search)
    {
        $query = $this->accountQuery()->orderBy('id', 'desc');
        $search->searchConditions($query);

        $paginator = $query->paginate(PER_PAGE);
        $paginator->getCollection()->transform(function (Account $account) {
            return (new AccountDetail($account))->toComponent();
        });

        return pagination($paginator);
    }

    /**
     * Store new an account
     *
     * @param AccountForm $form
     * @return bool|mixed
     */
    public function store(AccountForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Account $account */
                $account = $this->accountQuery()->createOrThrow($form->createAttributes());
                logger_info('Successful create account', ['id' => $account->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to create account: ', ['form_data' => $form->toArray()], $e);
        }
        return $result;
    }

    /**
     * findOrFail
     *
     * @param int $accountId
     * @return array
     */
    public function findOrFail(int $accountId): array
    {
        $account = $this->accountQuery()->findOrFail($accountId);

        return (new AccountDetail($account))->toComponent();
    }

    /**
     * update
     *
     * @param int $accountId
     * @param AccountForm $form
     * @return bool
     */
    public function update(int $accountId, AccountForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($accountId, $form) {
                $account = $this->accountQuery()->findOrFail($accountId);
                $account->updateOrThrow($form->updateAttributes());
                logger_info('Successfully updated account', ['id' => $account->id]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update account: ', [
                'account_id' => $accountId,
                'form_data' => $form->toArray()
            ], $e);
        }
        return $result;
    }

    /**
     * delete
     *
     * @param int $accountId
     * @return bool
     */
    public function delete(int $accountId): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($accountId) {
                $account = $this->accountQuery()->findOrFail($accountId);
                $account->delete();
                logger_info('Successfully deleted account', ['id' => $accountId]);

                return true;
            });
        } catch (Throwable $e) {
            logger_error('Failed to update account: ', [
                'account_id' => $accountId,
            ], $e);
        }
        return $result;
    }

    /**
     * @return Account|Builder
     */
    private function accountQuery(): Account|Builder
    {
        return Account::queryModel();
    }
}
