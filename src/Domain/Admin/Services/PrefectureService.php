<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Policy;
use Src\Domain\Admin\Models\Policy\PolicyDetail;
use Src\Domain\Admin\Models\Policy\PolicyForm;
use Src\Domain\Admin\Models\Policy\PolicySearchForm;
use DB;

class PrefectureService
{
    /**
     *
     * @param PolicySearchForm $form
     * @return array
     */
    public function fetchAll(PolicySearchForm $form)
    {
        $query = $this->policyQuery()->orderBy('id', 'desc');

        if ($form->getTitle()) {
            $query->where('title', 'like', '%' . $form->getTitle() . '%');
        }

        if ($form->getIsPublic() !== null) {
            $query->where('is_public', $form->getIsPublic());
        }

        $paginator = $query->paginate(PER_PAGE);

        $paginator->getCollection()->transform(function ($policy) {
            return (new PolicyDetail($policy))->toComponent();
        });

        return pagination($paginator);
    }

    private function policyQuery()
    {
        return Policy::query()->getModel();
    }
}
