<?php

namespace Src\Domain\Admin\Models\Policy;

use Src\Domain\FormModel;

class PolicyForm extends FormModel
{
    protected string $title;
    protected string $body;
    protected bool $is_public;

    public function __construct(array $input)
    {
        $this->title = array_get_string($input, 'title');
        $this->body = array_get_string($input, 'body');
        $this->is_public = array_get_bool($input, 'is_public', false);
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return $this->is_public;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'is_public' => $this->getIsPublic(),
        ];
    }

    /**
     * @return array
     */
    public function editAttributes(): array
    {
        return [
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'is_public' => $this->getIsPublic(),
        ];
    }
}
