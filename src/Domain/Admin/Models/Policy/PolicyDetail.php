<?php

namespace Src\Domain\Admin\Models\Policy;

use App\Eloquent\Policy;

/**
 * Class PolicyDetail
 * @package Src\Domain\Admin\Models\Policy
 */
class PolicyDetail
{
    /**
     * @var Policy
     */
    private $policy;

    /**
     * PolicyDetail constructor.
     * @param Policy $policy
     */
    public function __construct(Policy $policy)
    {
        $this->policy = $policy;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->policy->id;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->policy->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->policy->body;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return (bool)$this->policy->is_public;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->policy->created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->policy->updated_at;
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'isPublic' => $this->getIsPublic(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt()
        ];
    }
}
