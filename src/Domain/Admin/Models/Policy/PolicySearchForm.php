<?php

namespace Src\Domain\Admin\Models\Policy;

class PolicySearchForm
{
    private ?string $title;
    private ?bool $is_public;

    public function __construct(array $data)
    {
        $this->title = $data['title'] ?? null;
        $this->is_public = isset($data['is_public']) ? (bool)$data['is_public'] : null;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getIsPublic(): ?bool
    {
        return $this->is_public;
    }
}
