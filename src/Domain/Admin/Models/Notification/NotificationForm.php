<?php

namespace Src\Domain\Admin\Models\Notification;

use Carbon\Carbon;
use Src\Domain\FormModel;
use Src\Enums\NotificationType;

class NotificationForm extends FormModel
{
    protected string $title;
    protected string $body;
    protected Carbon $start_notification_at;
    protected ?Carbon $end_notification_at;

    public function __construct(array $input)
    {
        $this->title = array_get_string($input, 'title');
        $this->body = array_get_string($input, 'body');
        $this->start_notification_at = array_get_carbon($input, 'start_notification_at');
        $this->end_notification_at = array_get_carbon($input, 'end_notification_at');
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    /**
     * @return Carbon|null
     */
    public function getStartNotificationAt(): ?Carbon
    {
        return $this->start_notification_at;
    }

    /**
     * @return Carbon|null
     */
    public function getEndNotificationAt(): ?Carbon
    {
        return $this->end_notification_at;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'type' => NotificationType::PUBLIC,
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'start_notification_at' => $this->getStartNotificationAt(),
            'end_notification_at' => $this->getEndNotificationAt(),
        ];
    }

    /**
     * @return array
     */
    public function editAttributes(): array
    {
        return [
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'start_notification_at' => $this->getStartNotificationAt(),
            'end_notification_at' => $this->getEndNotificationAt(),
        ];
    }
}
