<?php

namespace Src\Domain\Admin\Models\Notification;

use App\Eloquent\Notification;

/**
 * Class NotificationDetail
 * @package Src\Domain\Admin\Models\Notification
 */
class NotificationDetail
{
    /**
     * @var Notification
     */
    private $notification;

    /**
     * NotificationDetail constructor.
     * @param Notification $notification
     */
    public function __construct(Notification $notification)
    {
        $this->notification = $notification;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->notification->id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->notification->type;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->notification->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->notification->body;
    }

    /**
     * @return int|null
     */
    public function getJobId(): ?int
    {
        return $this->notification->job_id;
    }

    /**
     * @return string
     */
    public function getStartNotificationAt(): string
    {
        return $this->notification->start_notification_at;
    }

    /**
     * @return string
     */
    public function getEndNotificationAt(): string
    {
        return $this->notification->end_notification_at;
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'type' => $this->getType(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'startNotificationAt' => $this->getStartNotificationAt(),
            'endNotificationAt' => $this->getEndNotificationAt()
        ];
    }
}
