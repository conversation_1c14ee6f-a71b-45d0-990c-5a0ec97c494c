<?php

namespace Src\Domain\Admin\Models\Notification;

class NotificationSearchForm
{
    private ?string $type;
    private ?string $title;

    public function __construct(array $data)
    {
        $this->type = $data['type'] ?? null;
        $this->title = $data['title'] ?? null;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }
} 