<?php

namespace Src\Domain\Admin\Models\Auth;

use Src\Domain\FormModel;

class LoginForm extends FormModel
{
    protected string $email;
    protected string $password;

    /**
     * LoginForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $input = $this->castFields($input);
        $this->email = $input['email'];
        $this->password = $input['password'];
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }
}
