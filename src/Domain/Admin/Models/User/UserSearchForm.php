<?php

namespace Src\Domain\Admin\Models\User;

use Illuminate\Database\Eloquent\Builder;
use Src\Domain\FormModel;
use Src\Utils\Util;

/**
 * Class UserSearchForm
 *
 * This class represents a form for searching users with various criteria.
 *
 * @package Src\Domain\Admin\Models\User
 */
class UserSearchForm extends FormModel
{
    // Search filter properties
    protected ?string $prefecture = null;
    protected ?string $user_status = null;
    protected ?string $residence_card_status = null;
    protected ?string $gender = null;
    protected ?string $keyword = null;
    protected ?int $job_participation_count = null;
    protected ?int $age_start = null;
    protected ?int $age_end = null;

    /**
     * UserSearchForm constructor.
     *
     * @param array $input The input data for initializing the form
     */
    public function __construct(array $input = [])
    {
        // Map input fields to properties
        $this->keyword = array_get_string($input, 'keyword');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->user_status = array_get_string($input, 'user_status');
        $this->residence_card_status = array_get_string($input, 'residence_card_status');
        $this->gender = array_get_string($input, 'gender');
        $this->job_participation_count = array_get_int($input, 'job_participation_count');
        $this->age_start = array_get_int($input, 'age_start');
        $this->age_end = array_get_int($input, 'age_end');
    }

    // Search filter getters
    public function getKeyword(): ?string { return $this->keyword; }
    public function getPrefecture(): ?string { return $this->prefecture; }
    public function getUserStatus(): ?string { return $this->user_status; }
    public function getResidenceCardStatus(): ?string { return $this->residence_card_status; }
    public function getGender(): ?string { return $this->gender; }
    public function getJobParticipationCount(): ?int { return $this->job_participation_count; }
    public function getAgeStart(): ?int { return $this->age_start; }
    public function getAgeEnd(): ?int { return $this->age_end; }

    /**
     * Add search conditions to the query based on the form input.
     *
     * @param Builder $query The query builder instance
     * @return Builder The modified query builder with search conditions applied
     */
    public function searchConditions(Builder $query): Builder
    {
        // Define search conditions as field-operator-value arrays
        $conditions = [
            ['prefecture', '=', $this->getPrefecture()],
            ['user_status', '=', $this->getUserStatus()],
            ['gender', '=', $this->getGender()],
            ['job_participation_count', '=', $this->getJobParticipationCount()],
        ];

        // Apply conditions to the query
        return Util::addSearchCondition($query, $conditions);
    }

    /**
     * Check if any search filters are applied
     *
     * @return bool True if at least one search filter is applied
     */
    public function hasFilters(): bool
    {
        return !empty($this->getPrefecture()) ||
               !empty($this->getUserStatus()) ||
               !empty($this->getGender()) ||
               !empty($this->getKeyword());
    }
}
