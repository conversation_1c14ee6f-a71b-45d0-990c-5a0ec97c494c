<?php

namespace Src\Domain\Admin\Models\User;

use Illuminate\Http\UploadedFile;
use Src\Domain\BaseForm;

/**
 * Class UserForm
 *
 * A unified form model that combines base and bank functionality
 * to reduce code duplication and improve maintainability.
 *
 * @package Src\Domain\Admin\Models\User
 */
class UserForm extends BaseForm
{
    // Form type constants
    public const TYPE_BASE = 'base';
    public const TYPE_BANK = 'bank';

    public const TYPE_FULL = 'full';

    /**
     * The type of form this instance represents
     */
    protected string $formType;

    // Personal information
    protected ?string $name;
    protected ?string $email;
    protected ?string $password;
    protected ?string $passwordConfirmation;
    protected ?string $nameKana;
    protected ?string $nameKanji;
    protected ?string $phoneNumber;
    protected ?string $gender;
    protected ?string $birthday;
    protected ?string $nationality;
    protected ?string $hasCertificate;
    protected ?string $japaneseLevel;
    protected ?string $arrivalDate;

    // Address information
    protected ?string $zipCode;
    protected ?string $prefecture;
    protected ?string $streetAddress;
    protected ?string $townAddress;
    protected ?string $trainStationName;

    // Emergency contact information
    protected ?string $emergencyName;
    protected ?string $emergencyRelation;
    protected ?string $emergencyPhoneNumber;

    // Bank information
    protected ?string $bankType;
    protected ?string $bankName;
    protected ?string $bankBranch;
    protected ?string $depositType;
    protected ?string $accountName;
    protected ?string $accountNumber;
    protected ?UploadedFile $atmImage;

    // File uploads
    protected ?UploadedFile $avatar;
    protected ?UploadedFile $healthCertificate;
    protected ?UploadedFile $frontCard;
    protected ?UploadedFile $backCard;
    protected ?UploadedFile $identification;
    protected ?UploadedFile $passportImage;

    // Residence information
    protected ?string $periodType;
    protected ?string $schoolName;
    protected ?string $periodOfStay;
    protected ?string $periodExpireAt;
    protected ?string $passportNumber;
    protected ?string $passportExpiredAt;

    /**
     * UserForm constructor
     *
     * @param array $input Form input data
     * @param string $formType The type of form to initialize (base or bank)
     */
    public function __construct(array $input = [], string $formType = self::TYPE_FULL)
    {
        $this->formType = $formType;

        // Initialize properties based on form type
        switch ($formType) {
            case self::TYPE_BASE:
                $this->initBaseForm($input);
                break;
            case self::TYPE_BANK:
                $this->initBankForm($input);
                break;

            default:
                $this->initBaseForm($input);
                $this->initBankForm($input);
        }
    }

    /**
     * Initialize base form properties
     *
     * @param array $input Form input data
     */
    protected function initBaseForm(array $input): void
    {
        // Map input fields to properties
        $this->name = array_get_string($input, 'name');
        $this->email = array_get_string($input, 'email');
        $this->password = array_get_string($input, 'password');
        $this->passwordConfirmation = array_get_string($input, 'password_confirmation');
        $this->nameKana = array_get_string($input, 'name_kana');
        $this->nameKanji = array_get_string($input, 'name_kanji');
        $this->phoneNumber = array_get_string($input, 'phone_number');
        $this->gender = array_get_string($input, 'gender');
        $this->birthday = array_get_string($input, 'birthday');
        $this->nationality = array_get_string($input, 'nationality');
        $this->hasCertificate = array_get_string($input, 'has_certificate');
        $this->japaneseLevel = array_get_string($input, 'japanese_level');
        $this->arrivalDate = array_get_string($input, 'arrival_date');
        $this->zipCode = array_get_string($input, 'zip_code');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->streetAddress = array_get_string($input, 'street_address');
        $this->townAddress = array_get_string($input, 'town_address');
        $this->trainStationName = array_get_string($input, 'train_station_name');
        $this->emergencyName = array_get_string($input, 'emergency_name');
        $this->emergencyRelation = array_get_string($input, 'emergency_relation');
        $this->emergencyPhoneNumber = array_get_string($input, 'emergency_phone_number');

        // File uploads
        $this->avatar = $input['avatar'] ?? null;
        $this->healthCertificate = $input['health_certificate'] ?? null;
        $this->frontCard = $input['front_card'] ?? null;
        $this->backCard = $input['back_card'] ?? null;
        $this->identification = $input['identification'] ?? null;
        $this->passportImage = $input['passport_image'] ?? null;

        // Residence information
        $this->periodType = array_get_string($input, 'period_type');
        $this->schoolName = array_get_string($input, 'school_name');
        $this->periodOfStay = array_get_string($input, 'period_of_stay');
        $this->periodExpireAt = array_get_string($input, 'period_expire_at');
        $this->passportNumber = array_get_string($input, 'passport_number');
        $this->passportExpiredAt = array_get_string($input, 'passport_expired_at');
    }

    /**
     * Initialize bank form properties
     *
     * @param array $input Form input data
     */
    protected function initBankForm(array $input): void
    {
        $this->bankType = array_get_string($input, 'bank_type');
        $this->bankName = array_get_string($input, 'bank_name');
        $this->bankBranch = array_get_string($input, 'bank_branch');
        $this->depositType = array_get_string($input, 'deposit_type');
        $this->accountName = array_get_string($input, 'account_name');
        $this->accountNumber = array_get_string($input, 'account_number');
        $this->atmImage = $input['atm_image'] ?? null;
    }

    /**
     * Create a new base form instance
     *
     * @param array $input Form input data
     * @return self
     */
    public static function createBaseForm(array $input = []): self
    {
        return new self($input, self::TYPE_BASE);
    }

    /**
     * Create a new bank form instance
     *
     * @param array $input Form input data
     * @return self
     */
    public static function createBankForm(array $input = []): self
    {
        return new self($input, self::TYPE_BANK);
    }

    /**
     * Get the form type
     *
     * @return string
     */
    public function getFormType(): string
    {
        return $this->formType;
    }

    // Personal information getters
    public function getName(): ?string
    {
        return $this->name;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function getPasswordConfirmation(): ?string
    {
        return $this->passwordConfirmation;
    }

    public function getNameKana(): ?string
    {
        return $this->nameKana;
    }

    public function getNameKanji(): ?string
    {
        return $this->nameKanji;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function getBirthday(): ?string
    {
        return $this->birthday;
    }

    public function getNationality(): ?string
    {
        return $this->nationality;
    }

    public function getHasCertificate(): ?string
    {
        return $this->hasCertificate;
    }

    public function getJapaneseLevel(): ?string
    {
        return $this->japaneseLevel;
    }

    public function getArrivalDate(): ?string
    {
        return $this->arrivalDate;
    }

    // Address information getters
    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    public function getPrefecture(): ?string
    {
        return $this->prefecture;
    }

    public function getStreetAddress(): ?string
    {
        return $this->streetAddress;
    }

    public function getTownAddress(): ?string
    {
        return $this->townAddress;
    }

    public function getTrainStationName(): ?string
    {
        return $this->trainStationName;
    }

    // Emergency contact information getters
    public function getEmergencyName(): ?string
    {
        return $this->emergencyName;
    }

    public function getEmergencyRelation(): ?string
    {
        return $this->emergencyRelation;
    }

    public function getEmergencyPhoneNumber(): ?string
    {
        return $this->emergencyPhoneNumber;
    }

    // Bank information getters
    public function getAtmImage(): ?UploadedFile
    {
        return $this->atmImage;
    }

    public function getBankType(): ?string
    {
        return $this->bankType;
    }

    public function getBankName(): ?string
    {
        return $this->bankName;
    }

    public function getBankBranch(): ?string
    {
        return $this->bankBranch;
    }

    public function getDepositType(): ?string
    {
        return $this->depositType;
    }

    public function getAccountName(): ?string
    {
        return $this->accountName;
    }

    public function getAccountNumber(): ?string
    {
        return $this->accountNumber;
    }

    // File upload getters
    public function getAvatar(): ?UploadedFile
    {
        return $this->avatar;
    }

    public function getHealthCertificate(): ?UploadedFile
    {
        return $this->healthCertificate;
    }

    public function getFrontCard(): ?UploadedFile
    {
        return $this->frontCard;
    }

    public function getBackCard(): ?UploadedFile
    {
        return $this->backCard;
    }

    public function getIdentification(): ?UploadedFile
    {
        return $this->identification;
    }

    public function getPassportImage(): ?UploadedFile
    {
        return $this->passportImage;
    }

    public function getPassport(): ?UploadedFile
    {
        return $this->passportImage;
    }

    // Residence information getters
    public function getPeriodType(): ?string
    {
        return $this->periodType;
    }

    public function getSchoolName(): ?string
    {
        return $this->schoolName;
    }

    public function getPeriodOfStay(): ?string
    {
        return $this->periodOfStay;
    }

    public function getPeriodExpireAt(): ?string
    {
        return $this->periodExpireAt;
    }

    public function getPassportNumber(): ?string
    {
        return $this->passportNumber;
    }

    public function getPassportExpiredAt(): ?string
    {
        return $this->passportExpiredAt;
    }

    /**
     * Get attributes for updating user base information
     *
     * @return array User attributes ready for database update
     */
    public function updateUserBaseAttributes(): array
    {
        return [
            // Personal information
            'name' => $this->getName(),
            'name_kana' => $this->getNameKana(),
            'name_kanji' => $this->getNameKanji(),
            'phone_number' => $this->getPhoneNumber(),
            'gender' => $this->getGender(),
            'birthday' => $this->getBirthday(),
            'nationality' => $this->getNationality(),
            'has_certificate' => $this->getHasCertificate(),
            'japanese_level' => $this->getJapaneseLevel(),
            'arrival_date' => $this->getArrivalDate(),

            // Address information
            'zip_code' => $this->getZipCode(),
            'prefecture' => $this->getPrefecture(),
            'street_address' => $this->getStreetAddress(),
            'town_address' => $this->getTownAddress(),
            'train_station_name' => $this->getTrainStationName(),

            // Emergency contact information
            'emergency_name' => $this->getEmergencyName(),
            'emergency_relation' => $this->getEmergencyRelation(),
            'emergency_phone_number' => $this->getEmergencyPhoneNumber(),
            'bank_type' => $this->getBankType(),
        ];
    }

    /**
     * Get attributes for updating user bank info
     *
     * @return array Bank attributes ready for database update
     */
    public function updateUserBankAttributes($atmImageId): array
    {
        return [
            'atm_image_id' => $atmImageId,
            'bank_name' => $this->getBankName(),
            'bank_branch' => $this->getBankBranch(),
            'deposit_type' => $this->getDepositType(),
            'account_name' => $this->getAccountName(),
            'account_number' => $this->getAccountNumber(),
        ];
    }

    /**
     * Get attributes for creating user bank info
     *
     * @return array Bank attributes ready for database creation
     */
    public function createUserBankAttributes($atmImageId): array
    {
        return $this->updateUserBankAttributes($atmImageId);
    }

    /**
     * Check if bank information is complete
     *
     * @return bool True if all required bank fields are filled
     */
    public function isBankInfoComplete(): bool
    {
        return !empty($this->getBankName()) &&
            !empty($this->getBankBranch()) &&
            !empty($this->getAccountName()) &&
            !empty($this->getAccountNumber());
    }

    /**
     * Create user attributes with code
     *
     * @param string $code User code
     * @return array User attributes for creation
     */
    public function createUserAttributes(string $code): array
    {
        return array_merge($this->updateUserBaseAttributes(), [
            'code' => $code,
            'email' => $this->getEmail(),
            'password' => bcrypt($this->getPassword()),
            'passport_number' => $this->getPassportNumber(),
            'passport_expired_at' => $this->getPassportExpiredAt(),
        ]);
    }

    /**
     * Create residence card attributes
     *
     * @return array Residence card attributes
     */
    public function createResidenceAttributes(): array
    {
        return [
            'period_type' => $this->getPeriodType(),
            'school_name' => $this->getSchoolName(),
            'period_of_stay' => $this->getPeriodOfStay(),
            'period_expire_at' => $this->getPeriodExpireAt(),
        ];
    }
}
