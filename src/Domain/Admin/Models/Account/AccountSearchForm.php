<?php

namespace Src\Domain\Admin\Models\Account;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\FormModel;
use Src\Utils\Util;

class AccountSearchForm extends FormModel
{
  public $name;
  public $login_id;
  public $role_div;

  /**
   * Summary of __construct
   * @param array $input
   */
  public function __construct(array $input = []) {
    $this->name = array_get_string($input, 'name');
    $this->login_id = array_get_string($input, 'login_id');
    $this->role_div = array_get_string($input, 'role_div');
  }

  /**
   * Summary of getKeyword
   * @return string|null
   */
  public function getName(): ?string
  {
    return $this->name;
  }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return string|null
     */
    public function getRoleDiv(): ?string
    {
        return $this->role_div;
    }

    public function searchConditions(Builder $query): Builder
    {
        $conditions = [
            ['name', 'like', "%".$this->getName()."%"],
            ['login_id', 'like', "%".$this->getLoginId()."%"],
            ['role_div', '=', $this->getRoleDiv()],
        ];

        return Util::addSearchCondition($query, $conditions);
    }
}
