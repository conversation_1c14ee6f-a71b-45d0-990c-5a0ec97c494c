<?php

namespace Src\Domain\Admin\Models\Account;

use Src\Domain\FormModel;
use Src\Enums\ApprovalStatus;

/**
 * Class AccountForm
 * @package Src\Domain\Admin\Models\Account
 */
class AccountForm extends FormModel
{
    protected string $name;
    protected string $email;
    protected string $password;
    protected string $roleDiv;

    protected string $department;

    public function __construct(array $input = [])
    {
        $this->name = array_get_string($input, 'name');
        $this->email = array_get_string($input, 'email');
        $this->password = array_get_string($input, 'password');
        $this->roleDiv = array_get_string($input, 'role_div');
        $this->department = array_get_string($input, 'department');
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return string
     */
    public function getDepartment(): string
    {
        return $this->department;
    }

    /**
     * @return string|null
     */
    public function getRoleDiv(): ?string
    {
        return $this->roleDiv;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'password' => bcrypt($this->getPassword()),
            'department' => $this->getDepartment(),
            'role_div' => $this->getRoleDiv(),
        ];
    }

    /**
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'department' => $this->getDepartment(),
            'role_div' => $this->getRoleDiv()
        ];
    }
}
