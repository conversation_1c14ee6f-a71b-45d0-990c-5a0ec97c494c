<?php

namespace Src\Domain\Admin\Models\Faq;

use Src\Domain\FormModel;

class FaqForm extends FormModel
{
    protected $question;
    protected $answer;
    protected $is_public;

    public function __construct(array $input)
    {
        $this->question = array_get_string($input, 'question');
        $this->answer = array_get_string($input, 'answer');
        $this->is_public = array_get_bool($input, 'is_public', false);
    }

    /**
     * @return string
     */
    public function getQuestion(): string
    {
        return $this->question;
    }

    /**
     * @return string
     */
    public function getAnswer(): string
    {
        return $this->answer;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return $this->is_public;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'question' => $this->getQuestion(),
            'answer' => $this->getAnswer(),
            'is_public' => $this->getIsPublic(),
        ];
    }

    /**
     * @return array
     */
    public function editAttributes(): array
    {
        return [
            'question' => $this->getQuestion(),
            'answer' => $this->getAnswer(),
            'is_public' => $this->getIsPublic(),
        ];
    }
}
