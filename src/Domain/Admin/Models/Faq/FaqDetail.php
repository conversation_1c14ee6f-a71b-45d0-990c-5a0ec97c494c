<?php

namespace Src\Domain\Admin\Models\Faq;

use App\Eloquent\Faq;

/**
 * Class FaqDetail
 * @package Src\Domain\Admin\Models\Faq
 */
class FaqDetail
{
    /**
     * @var Faq
     */
    private $faq;

    /**
     * FaqDetail constructor.
     * @param Faq $faq
     */
    public function __construct(Faq $faq)
    {
        $this->faq = $faq;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->faq->id;
    }

    /**
     * @return string
     */
    public function getQuestion(): string
    {
        return $this->faq->question;
    }

    /**
     * @return string
     */
    public function getAnswer(): string
    {
        return $this->faq->answer;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return (bool)$this->faq->is_public;
    }

    /**
     * @return int|null
     */
    public function getAccountId(): ?int
    {
        return $this->faq->account_id;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->faq->created_at;
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->faq->updated_at;
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'question' => $this->getQuestion(),
            'answer' => $this->getAnswer(),
            'isPublic' => $this->getIsPublic(),
            'accountId' => $this->getAccountId(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt()
        ];
    }
}
