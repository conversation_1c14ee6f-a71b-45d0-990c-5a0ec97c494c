<?php

namespace Src\Domain\Admin\Models\Job;

use Illuminate\Database\Eloquent\Builder;
use Src\Utils\Util;

/**
 * Class JobSearchForm
 * @package Src\Domain\Admin\Models\Job
 */
class JobSearchForm
{
    private ?string $title;
    private ?string $prefecture;
    protected ?int $time_start;
    protected ?int $time_end;
    private ?bool $is_filled;

    /**
     * JobSearchForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->title = array_get_string($input, 'title');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->time_start = array_get_int($input, 'time_start');
        $this->time_end = array_get_int($input, 'time_end');
        $this->is_filled = array_get_bool($input, 'is_filled');
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @return string|null
     */
    public function getPrefecture(): ?string
    {
        return $this->prefecture;
    }

    /**
     * @return int|null
     */
    public function getTimeStart(): ?int
    {
        return $this->time_start;
    }

    /**
     * @return int|null
     */
    public function getTimeEnd(): ?int
    {
        return $this->time_end;
    }

    /**
     * @return bool|null
     */
    public function getIsFilled(): ?bool
    {
        return $this->is_filled;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function searchConditions(Builder $query): Builder
    {
        $conditions = [
            ['title', 'like', '%'.$this->getTitle().'%'],
            ['prefecture', '=', $this->getPrefecture()],
            ['time_start', '>=', $this->getTimeStart()],
            ['time_end', '<=', $this->getTimeEnd()],
            ['is_filled', '=', $this->getIsFilled()]
        ];
        return Util::addSearchCondition($query, $conditions);
    }
}
