<?php

namespace Src\Domain\Admin\Models\Job;

use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;
use Src\Traits\EditorImageTrait;

/**
 * Class JobForm
 * @package Src\Domain\Admin\Models\Job
 */
class JobForm extends FormModel
{
    use EditorImageTrait;

    protected int $group_id;
    protected bool $is_recommended;
    protected string $recruitment_type;
    protected string $employer_email;
    protected string $employer_name;
    protected string $employer_phone_number;
    protected int $category_id;
    protected string $type;
    protected bool $is_public;
    protected bool $is_instant;
    protected string $title;
    protected string $description;
    protected ?string $benefits;
    protected ?string $time_start;
    protected ?string $time_end;
    protected ?int $age;
    protected ?string $gender;
    protected ?int $quantity;
    protected string $certificate_level;
    protected string $prefecture;
    protected string $address;
    protected string $salary_type;
    protected ?int $salary;
    protected string $travel_fee_type;
    protected ?int $travel_fee;
    protected string $recruit_start_at;
    protected string $recruit_expired_at;
    protected string $job_start_at;
    protected ?UploadedFile $thumbnail;
    protected ?array $images;

    protected ?array $deleted_image_ids;

    /**
     * JobForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input)
    {
        $this->group_id = array_get_int($input, 'group_id');
        $this->is_recommended = array_get_bool($input, 'is_recommended', false);
        $this->recruitment_type = array_get_string($input, 'recruitment_type');
        $this->employer_email = array_get_string($input, 'employer_email');
        $this->employer_name = array_get_string($input, 'employer_name');
        $this->employer_phone_number = array_get_string($input, 'employer_phone_number');
        $this->category_id = array_get_int($input, 'category_id');
        $this->type = array_get_string($input, 'type');
        $this->is_public = array_get_bool($input, 'is_public', false);
        $this->is_instant = array_get_bool($input, 'is_instant', false);
        $this->title = array_get_string($input, 'title');
        $this->description = array_get_string($input, 'description');
        $this->benefits = array_get_string($input, 'benefits', null);
        $this->time_start = array_get_string($input, 'time_start', null);
        $this->time_end = array_get_string($input, 'time_end', null);
        $this->age = array_get_int($input, 'age', null);
        $this->gender = array_get_string($input, 'gender', null);
        $this->quantity = array_get_int($input, 'quantity', null);
        $this->certificate_level = array_get_string($input, 'certificate_level');
        $this->prefecture = array_get_string($input, 'prefecture');
        $this->address = array_get_string($input, 'address');
        $this->salary_type = array_get_string($input, 'salary_type');
        $this->salary = array_get_int($input, 'salary', null);
        $this->travel_fee_type = array_get_string($input, 'travel_fee_type');
        $this->travel_fee = array_get_int($input, 'travel_fee', null);
        $this->recruit_start_at = array_get_string($input, 'recruit_start_at');
        $this->recruit_expired_at = array_get_string($input, 'recruit_expired_at');
        $this->job_start_at = array_get_string($input, 'job_start_at');
        $this->thumbnail = $input['thumbnail'] ?? null;
        $this->images = $input['images'] ?? null;
        $this->deleted_image_ids = array_get_array($input, 'deleted_image_ids', []);
    }

    /**
     * @return int
     */
    public function getGroupId(): int
    {
        return $this->group_id;
    }

    /**
     * @return bool
     */
    public function getIsRecommended(): bool
    {
        return $this->is_recommended;
    }

    /**
     * @return string
     */
    public function getRecruitmentType(): string
    {
        return $this->recruitment_type;
    }

    /**
     * @return string
     */
    public function getEmployerEmail(): string
    {
        return $this->employer_email;
    }

    /**
     * @return string
     */
    public function getEmployerName(): string
    {
        return $this->employer_name;
    }

    /**
     * @return string
     */
    public function getEmployerPhoneNumber(): string
    {
        return $this->employer_phone_number;
    }

    /**
     * @return int
     */
    public function getCategoryId(): int
    {
        return $this->category_id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return $this->is_public;
    }

    /**
     * @return bool
     */
    public function getIsInstant(): bool
    {
        return $this->is_instant;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @return string|null
     */
    public function getBenefits(): ?string
    {
        return $this->benefits;
    }

    /**
     * @return string|null
     */
    public function getTimeStart(): ?string
    {
        return $this->time_start;
    }

    /**
     * @return string|null
     */
    public function getTimeEnd(): ?string
    {
        return $this->time_end;
    }

    /**
     * @return int|null
     */
    public function getAge(): ?int
    {
        return $this->age;
    }

    /**
     * @return string|null
     */
    public function getGender(): ?string
    {
        return $this->gender;
    }

    /**
     * @return int|null
     */
    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    /**
     * @return string
     */
    public function getCertificateLevel(): string
    {
        return $this->certificate_level;
    }

    /**
     * @return string
     */
    public function getPrefecture(): string
    {
        return $this->prefecture;
    }

    /**
     * @return string
     */
    public function getAddress(): string
    {
        return $this->address;
    }

    /**
     * @return string
     */
    public function getSalaryType(): string
    {
        return $this->salary_type;
    }

    /**
     * @return int|null
     */
    public function getSalary(): ?int
    {
        return $this->salary;
    }

    /**
     * @return string
     */
    public function getTravelFeeType(): string
    {
        return $this->travel_fee_type;
    }

    /**
     * @return int|null
     */
    public function getTravelFee(): ?int
    {
        return $this->travel_fee;
    }

    public function getRecruitStartAt(): string
    {
        return $this->recruit_start_at;
    }

    /**
     * @return string
     */
    public function getRecruitExpiredAt(): string
    {
        return $this->recruit_expired_at;
    }

    /**
     * @return string
     */
    public function getJobStartAt(): string
    {
        return $this->job_start_at;
    }

    /**
     * @return UploadedFile|null
     */
    public function getThumbnail(): ?UploadedFile
    {
        return $this->thumbnail;
    }

    /**
     * @return array|null
     */
    public function getImages(): ?array
    {
        return $this->images;
    }

    /**
     * @return array|null
     */
    public function getDeletedImageIds(): ?array
    {
        return $this->deleted_image_ids;
    }

    /**
     * @return string
     */
    public function getNewDescription(): string
    {
        return $this->processEditorImages($this->getDescription());
    }

    /**
     * @return string|null
     */
    public function getNewBenefits(): ?string
    {
        if ($this->getBenefits()) {
            return $this->processEditorImages($this->getBenefits());
        }
        return null;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'group_id' => $this->getGroupId(),
            'is_recommended' => $this->getIsRecommended(),
            'recruitment_type' => $this->getRecruitmentType(),
            'employer_email' => $this->getEmployerEmail(),
            'employer_name' => $this->getEmployerName(),
            'employer_phone_number' => $this->getEmployerPhoneNumber(),
            'category_id' => $this->getCategoryId(),
            'type' => $this->getType(),
            'is_public' => $this->getIsPublic(),
            'is_instant' => $this->getIsInstant(),
            'title' => $this->getTitle(),
            'description' => $this->getNewDescription(),
            'benefits' => $this->getNewBenefits(),
            'time_start' => $this->getTimeStart(),
            'time_end' => $this->getTimeEnd(),
            'age' => $this->getAge(),
            'gender' => $this->getGender(),
            'quantity' => $this->getQuantity(),
            'certificate_level' => $this->getCertificateLevel(),
            'prefecture' => $this->getPrefecture(),
            'address' => $this->getAddress(),
            'salary_type' => $this->getSalaryType(),
            'salary' => $this->getSalary(),
            'travel_fee_type' => $this->getTravelFeeType(),
            'travel_fee' => $this->getTravelFee(),
            'recruit_start_at' => $this->getRecruitStartAt(),
            'recruit_expired_at' => $this->getRecruitExpiredAt(),
            'job_start_at' => $this->getJobStartAt(),
        ];
    }

    /**
     * @return array
     */
    public function editAttributes(): array
    {
        return $this->createAttributes();
    }
}
