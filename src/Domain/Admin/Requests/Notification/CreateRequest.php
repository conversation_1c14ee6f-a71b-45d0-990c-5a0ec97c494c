<?php

namespace Src\Domain\Admin\Requests\Notification;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Notification\NotificationForm;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'max:255'
            ],
            'body' => [
                'required',
                'string'
            ],
            'start_notification_at' => [
                'required',
                'date'
            ],
            'end_notification_at' => [
                'required',
                'date',
                'after:start_notification_at'
            ],
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'title' => __('models/notification.field.title'),
            'body' => __('models/notification.field.body'),
            'type' => __('models/notification.field.type'),
            'job_id' => __('models/notification.field.jobId'),
            'start_notification_at' => __('models/notification.field.startNotificationAt'),
            'end_notification_at' => __('models/notification.field.endNotificationAt'),
        ];
    }

    /**
     * Get the validated form data.
     */
    public function validatedForm(): NotificationForm
    {
        return new NotificationForm($this->validated());
    }
}
