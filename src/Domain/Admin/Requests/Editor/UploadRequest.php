<?php

namespace Src\Domain\Admin\Requests\Editor;

use Src\Domain\Admin\Requests\FormRequest;
use Src\Domain\Admin\Models\TextEditor\UploadForm;

/**
 * Class UploadRequest
 * @package Src\Domain\Admin\Requests\Editor
 */
class UploadRequest extends FormRequest
{
    /**
     * Authorization
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Validation rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'upload' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg,gif,webp',
                'max:5120', // 5MB
            ],
        ];
    }

    /**
     * Attribute names
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'upload' => __('common.image'),
        ];
    }

    /**
     * Custom error messages
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'upload.required' => __('validation.required', ['attribute' => 'image']),
            'upload.image' => __('validation.image', ['attribute' => 'image']),
            'upload.mimes' => __('validation.mimes', ['attribute' => 'image', 'values' => 'jpeg, png, jpg, gif, webp']),
            'upload.max' => __('validation.max.file', ['attribute' => 'image', 'max' => '5MB']),
        ];
    }

    /**
     * Return validated form
     *
     * @return UploadForm
     */
    public function validatedForm(): UploadForm
    {
        return new UploadForm($this->validated());
    }
}
