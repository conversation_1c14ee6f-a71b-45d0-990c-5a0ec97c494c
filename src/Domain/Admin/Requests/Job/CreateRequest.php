<?php

namespace Src\Domain\Admin\Requests\Job;

use Src\Domain\Admin\Models\Job\JobForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class CreateRequest
 * @package Src\Domain\Admin\Requests\Job
 */
class CreateRequest extends BaseRequest
{

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'thumbnail' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
            ],
            'images' => [
                'required',
                'array'
            ],
            'images.*' => [
                'image',
                'mimes:jpeg,png,jpg',
            ],
        ]);
    }
}
