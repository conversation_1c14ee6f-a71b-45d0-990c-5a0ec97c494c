<?php

namespace Src\Domain\Admin\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest as Base;
use Illuminate\Validation\ValidationException;

/**
 * Class FormRequest
 * @package Src\Domain\Admin\Requests
 */
abstract class FormRequest extends Base
{
    /**
     * @return mixed
     */
    abstract public function validatedForm();

    /**
     * Failed Validation
     *
     * @param Validator $validator
     */
    protected function failedValidation(Validator $validator)
    {
        throw (new ValidationException($validator));
    }
}
