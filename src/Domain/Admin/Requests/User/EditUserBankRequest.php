<?php

namespace Src\Domain\Admin\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\User\UserForm;

class EditUserBankRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'bank_type' => [
                'required',
                'string',
            ],
            'atm_image' => [
                'required_if:bank_type,BANK',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'bank_name' => [
                'required_if:bank_type,BANK',
                'string',
            ],
            'bank_branch' => [
                'required_if:bank_type,BANK',
                'string',
            ],
            'deposit_type' => [
                'required_if:bank_type,BANK',
                'string',
            ],
            'account_name' => [
                'required_if:bank_type,BANK',
                'string',
            ],
            'account_number' => [
                'required_if:bank_type,BANK',
                'string',
            ],
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            'bank_type' => __('models/user.field.bankType'),
            'atm_image' => __('models/user_bank.field.atm'),
            'bank_name' => __('models/user_bank.field.bankName'),
            'bank_branch' => __('models/user_bank.field.bankBranch'),
            'deposit_type' => __('models/user_bank.field.depositType'),
            'account_name' => __('models/user_bank.field.accountName'),
            'account_number' => __('models/user_bank.field.accountNumber'),
        ];
    }

    /**
     * Get validated form
     *
     * @return UserForm
     */
    public function validatedForm(): UserForm
    {
        return UserForm::createBankForm($this->validated());
    }
}
