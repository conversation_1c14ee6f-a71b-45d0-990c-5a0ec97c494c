<?php

namespace Src\Domain\Admin\Requests\User;

use Illuminate\Validation\Rule;
use Src\Domain\Admin\Models\User\UserForm;
use Src\Domain\Admin\Requests\FormRequest;
use Src\Enums\PeriodType;
use Src\Enums\UserBankType;

class CreateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
            ],
            'email' => [
                'required',
                'string',
                'email',
            ],
            'name_kana' => [
                'required',
                'string',
            ],
            'name_kanji' => [
                'required',
                'string',
            ],
            'phone_number' => [
                'required',
                'string',
            ],
            'gender' => [
                'required',
                'string',
            ],
            'birthday' => [
                'required',
                'date',
            ],
            'nationality' => [
                'required',
                'string',
            ],
            'has_certificate' => [
                'required',
                'boolean',
            ],
            'japanese_level' => [
                'required',
                'string',
            ],
            'arrival_date' => [
                'required',
                'string',
            ],
            'zip_code' => [
                'required',
                'string',
            ],
            'prefecture' => [
                'required',
                'string',
            ],
            'street_address' => [
                'required',
                'string',
            ],
            'town_address' => [
                'required',
                'string',
            ],
            'train_station_name' => [
                'required',
                'string',
            ],
            'emergency_name' => [
                'nullable',
                'string',
            ],
            'emergency_relation' => [
                'nullable',
                'string',
            ],
            'emergency_phone_number' => [
                'nullable',
                'string',
            ],
            'bank_type' => [
                'required',
                'string',
            ],
            'atm_image' => [
                'nullable',
                'required_if:bank_type,BANK',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'bank_name' => [
                'required_if:bank_type,BANK',
            ],
            'bank_branch' => [
                'required_if:bank_type,BANK',
            ],
            'deposit_type' => [
                'required_if:bank_type,BANK',
            ],
            'account_name' => [
                'required_if:bank_type,BANK',
            ],
            'account_number' => [
                'required_if:bank_type,BANK',
            ],
            'avatar' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'health_certificate' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'password' => [
                'required',
                'string',
                'min:8',
            ],
            'password_confirmation' => [
                'required',
                'confirmed:password',
            ],
            'front_card' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'back_card' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'period_type' => [
                'required',
            ],
            'period_of_stay' => [
                'required',
            ],
            'school_name' => [
                'required_if:period_type,SCHOOL',
            ],
            'identification' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'period_expire_at' => [
                'required',
            ],
            'passport_image' => [
                'required',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],
            'passport_number' => [
                'required',
                'string'
            ],
            'passport_expired_at' => [
                'required',
                'date'
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('models/user.field.name'),
            'email' => __('models/user.field.email'),
            'name_kana' => __('models/user.field.nameKana'),
            'name_kanji' => __('models/user.field.nameKanji'),
            'phone_number' => __('models/user.field.phone'),
            'gender' => __('models/user.field.gender'),
            'birthday' => __('models/user.field.birthday'),
            'nationality' => __('models/user.field.national'),
            'japanese_level' => __('models/user.field.japaneseLevel'),
            'arrival_date' => __('models/user.field.comingDate'),
            'zip_code' => __('models/user.field.zipcode'),
            'prefecture' => __('models/user.field.prefecture'),
            'street_address' => __('models/user.field.streetAddress'),
            'train_station_name' => __('models/user.field.trainStationName'),
            'emergency_name' => __('models/user.field.emergencyName'),
            'emergency_relation' => __('models/user.field.emergencyRelation'),
            'emergency_phone_number' => __('models/user.field.emergencyPhone'),
            'bank_type' => __('models/user.field.bankType'),
            'atm_image' => __('models/user_bank.field.atm'),
            'bank_name' => __('models/user_bank.field.bankName'),
            'bank_branch' => __('models/user_bank.field.bankBranch'),
            'deposit_type' => __('models/user_bank.field.depositType'),
            'account_name' => __('models/user_bank.field.accountName'),
            'account_number' => __('models/user_bank.field.accountNumber'),
            'avatar' => __('models/user.field.avatar'),
            'health_certificate' => __('models/user.field.healthCertificate'),
            'password' => __('models/user.field.password'),
            'password_confirmation' => __('models/user.field.confirmPassword'),
            'front_card' => __('models/residence_card.field.frontCard'),
            'back_card' => __('models/residence_card.field.backCard'),
            'period_type' => __('models/residence_card.field.periodType'),
            'period_of_stay' => __('models/residence_card.field.periodOfStay'),
            'school_name' => __('models/residence_card.field.schoolName'),
            'identification' => $this->period_type == PeriodType::STUDENT ? __('models/residence_card.field.studentCard') : __('models/residence_card.field.identification'),
            'period_expire_at' => __('models/residence_card.field.periodExpireAt'),
            'passport_image' => __('models/user.field.passportImage'),
            'passport_number' => __('models/user.field.passportNumber'),
            'passport_expired_at' => __('models/user.field.passportExpiredAt'),
        ];
    }

    /**
     * Get the validated form model.
     *
     * @return UserForm
     */
    public function validatedForm(): UserForm
    {
        // Create a full form with all data
        return new UserForm($this->validated(), UserForm::TYPE_FULL);
    }
}
