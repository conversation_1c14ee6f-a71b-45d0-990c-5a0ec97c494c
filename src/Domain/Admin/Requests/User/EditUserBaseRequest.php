<?php

namespace Src\Domain\Admin\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\User\UserForm;

class EditUserBaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
            ],
            'name_kana' => [
                'required',
                'string',
            ],
            'name_kanji' => [
                'required',
                'string',
            ],
            'phone_number' => [
                'required',
                'string',
            ],
            'gender' => [
                'required',
                'string',
            ],
            'birthday' => [
                'required',
                'date',
            ],
            'nationality' => [
                'required',
                'string',
            ],
            'has_certificate' => [
                'required',
                'boolean',
            ],
            'japanese_level' => [
                'required',
                'string',
            ],
            'arrival_date' => [
                'required',
                'string',
            ],
            'zip_code' => [
                'required',
                'string',
            ],
            'prefecture' => [
                'required',
                'string',
            ],
            'street_address' => [
                'required',
                'string',
            ],
            'town_address' => [
                'required',
                'string',
            ],
            'train_station_name' => [
                'required',
                'string',
            ],
            'emergency_name' => [
                'nullable',
                'string',
            ],
            'emergency_relation' => [
                'nullable',
                'string',
            ],
            'emergency_phone_number' => [
                'nullable',
                'string',
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => __('models/user.field.name'),
            'email' => __('models/user.field.email'),
            'name_kana' => __('models/user.field.nameKana'),
            'name_kanji' => __('models/user.field.nameKanji'),
            'phone_number' => __('models/user.field.phone'),
            'gender' => __('models/user.field.gender'),
            'birthday' => __('models/user.field.birthday'),
            'nationality' => __('models/user.field.national'),
            'japanese_level' => __('models/user.field.japaneseLevel'),
            'arrival_date' => __('models/user.field.comingDate'),
            'zip_code' => __('models/user.field.zipcode'),
            'prefecture' => __('models/user.field.prefecture'),
            'street_address' => __('models/user.field.streetAddress'),
            'train_station_name' => __('models/user.field.trainStationName'),
            'emergency_name' => __('models/user.field.emergencyName'),
            'emergency_relation' => __('models/user.field.emergencyRelation'),
            'emergency_phone_number' => __('models/user.field.emergencyPhone'),
        ];
    }

    /**
     * Get the validated form model.
     *
     * @return UserForm
     */
    public function validatedForm(): UserForm
    {
        return UserForm::createBaseForm($this->validated());
    }
}
