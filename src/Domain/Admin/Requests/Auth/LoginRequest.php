<?php

namespace Src\Domain\Admin\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Auth\LoginForm;

/**
 * Class LoginRequest
 * @package Src\Domain\Admin\Requests\Auth
 */
class LoginRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                'max:100'
            ],
            'password' => [
                'required',
                'string',
                'max:255'
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'email' => __('models/auth.login.email'),
            'password' => __('models/auth.login.password'),
        ];
    }

    /**
     * @return LoginForm
     */
    public function validatedForm(): LoginForm
    {
        return new LoginForm($this->validated());
    }
}
