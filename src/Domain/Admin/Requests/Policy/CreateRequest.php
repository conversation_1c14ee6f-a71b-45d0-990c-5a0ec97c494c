<?php

namespace Src\Domain\Admin\Requests\Policy;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Policy\PolicyForm;

class CreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'max:255'
            ],
            'body' => [
                'required',
                'string'
            ],
            'is_public' => [
                'boolean'
            ]
        ];
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'title' => __('models/policy.field.title'),
            'body' => __('models/policy.field.body'),
            'is_public' => __('models/policy.field.isPublic'),
        ];
    }

    /**
     * Get the validated form data.
     */
    public function validatedForm(): PolicyForm
    {
        return new PolicyForm($this->validated());
    }
}
